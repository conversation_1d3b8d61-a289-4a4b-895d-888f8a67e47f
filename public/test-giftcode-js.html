<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Giftcode JavaScript</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(20px);
        }
        .section {
            background: rgba(255, 255, 255, 0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .item-card {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .item-card:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: #63b3ed;
            transform: translateY(-2px);
        }
        .item-icon {
            font-size: 2rem;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
        .item-info {
            flex: 1;
        }
        .item-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .item-category {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        .add-btn {
            background: linear-gradient(135deg, #3182ce, #2c5282);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .add-btn:hover {
            transform: scale(1.05);
        }
        .selected-items {
            background: rgba(72, 187, 120, 0.2);
            border: 2px solid rgba(72, 187, 120, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .selected-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin: 10px 0;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .quantity-btn {
            background: #3182ce;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 5px;
            cursor: pointer;
        }
        .quantity-input {
            width: 60px;
            text-align: center;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .remove-btn {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .output-area {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        .output-textarea {
            width: 100%;
            height: 150px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 10px;
            font-family: 'Courier New', monospace;
        }
        .btn {
            background: linear-gradient(135deg, #3182ce, #2c5282);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.2s ease;
        }
        .btn:hover {
            transform: translateY(-1px);
        }
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .success {
            background: rgba(72, 187, 120, 0.3);
            border: 1px solid rgba(72, 187, 120, 0.5);
        }
        .error {
            background: rgba(239, 68, 68, 0.3);
            border: 1px solid rgba(239, 68, 68, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-gift"></i> Test Giftcode JavaScript Functionality</h1>
        
        <div class="section">
            <h2>🎮 Available Items</h2>
            <div id="items-grid" class="items-grid">
                <!-- Items will be loaded here -->
            </div>
        </div>

        <div class="section">
            <h2>🛒 Selected Items (<span id="selected-count">0</span>)</h2>
            <div id="selected-items" class="selected-items">
                <p>No items selected yet. Click on items above to add them.</p>
            </div>
        </div>

        <div class="section">
            <h2>📝 Generated Output</h2>
            <button class="btn" onclick="generateOutput()">
                <i class="fas fa-code"></i> Generate Giftcode Format
            </button>
            <div class="output-area">
                <textarea id="output" class="output-textarea" readonly placeholder="Generated giftcode format will appear here..."></textarea>
            </div>
        </div>

        <div class="section">
            <h2>🧪 Test Controls</h2>
            <button class="btn" onclick="addRandomItems()">
                <i class="fas fa-random"></i> Add Random Items
            </button>
            <button class="btn" onclick="clearAllItems()">
                <i class="fas fa-trash"></i> Clear All
            </button>
            <button class="btn" onclick="testQuantities()">
                <i class="fas fa-plus-circle"></i> Test Quantities
            </button>
            <div id="status"></div>
        </div>
    </div>

    <script>
        // Test data
        const testItems = {
            14: { name: 'Jewel of Bless', category: 'Jewels', icon: '💎', description: 'Tăng tỷ lệ thành công khi nâng cấp' },
            15: { name: 'Jewel of Soul', category: 'Jewels', icon: '💠', description: 'Dùng để nâng cấp item' },
            16: { name: 'Jewel of Life', category: 'Jewels', icon: '🔮', description: 'Tăng độ bền tối đa của item' },
            22: { name: 'Jewel of Creation', category: 'Jewels', icon: '✨', description: 'Tạo ra socket cho item' },
            31: { name: 'Jewel of Guardian', category: 'Jewels', icon: '🛡️', description: 'Bảo vệ item khỏi bị hỏng' },
            260: { name: 'Wings of Elf', category: 'Wings', icon: '🪶', description: 'Cánh thiên thần cấp 1' },
            261: { name: 'Wings of Heaven', category: 'Wings', icon: '🕊️', description: 'Cánh thiên đường cấp 1' },
            262: { name: 'Wings of Satan', category: 'Wings', icon: '🦇', description: 'Cánh ác ma cấp 1' },
            700: { name: 'Bound Diamond', category: 'Currency', icon: '💎', description: 'Kim cương ràng buộc' },
            701: { name: 'Free Diamond', category: 'Currency', icon: '💠', description: 'Kim cương tự do' },
            12: { name: 'Zen', category: 'Currency', icon: '💰', description: 'Tiền tệ trong game' },
            0: { name: 'Apple', category: 'Potions', icon: '🍎', description: 'Hồi phục HP nhỏ' },
            1: { name: 'Small Healing Potion', category: 'Potions', icon: '🧪', description: 'Hồi phục HP' },
            3: { name: 'Large Healing Potion', category: 'Potions', icon: '🧪', description: 'Hồi phục HP lớn' }
        };

        let selectedItems = [];

        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => status.innerHTML = '', 3000);
        }

        function loadItems() {
            const grid = document.getElementById('items-grid');
            grid.innerHTML = '';

            Object.entries(testItems).forEach(([id, item]) => {
                const card = document.createElement('div');
                card.className = 'item-card';
                card.innerHTML = `
                    <div class="item-icon">${item.icon}</div>
                    <div class="item-info">
                        <div class="item-name">${item.name}</div>
                        <div class="item-category">${item.category} (ID: ${id})</div>
                    </div>
                    <button class="add-btn" onclick="addItem(${id})">
                        <i class="fas fa-plus"></i> Add
                    </button>
                `;
                grid.appendChild(card);
            });

            showStatus(`✅ Loaded ${Object.keys(testItems).length} items`);
        }

        function addItem(itemId) {
            const item = testItems[itemId];
            if (!item) return;

            const existingIndex = selectedItems.findIndex(selected => selected.id == itemId);
            if (existingIndex !== -1) {
                selectedItems[existingIndex].quantity += 1;
            } else {
                selectedItems.push({
                    id: itemId,
                    name: item.name,
                    icon: item.icon,
                    category: item.category,
                    quantity: 1
                });
            }

            updateSelectedDisplay();
            showStatus(`✅ Added ${item.name}`);
        }

        function removeItem(itemId) {
            const item = selectedItems.find(item => item.id == itemId);
            selectedItems = selectedItems.filter(item => item.id != itemId);
            updateSelectedDisplay();
            showStatus(`🗑️ Removed ${item.name}`);
        }

        function updateQuantity(itemId, quantity) {
            const index = selectedItems.findIndex(item => item.id == itemId);
            if (index !== -1) {
                selectedItems[index].quantity = Math.max(1, parseInt(quantity) || 1);
                updateSelectedDisplay();
            }
        }

        function updateSelectedDisplay() {
            const container = document.getElementById('selected-items');
            const countElement = document.getElementById('selected-count');
            
            countElement.textContent = selectedItems.length;

            if (selectedItems.length === 0) {
                container.innerHTML = '<p>No items selected yet. Click on items above to add them.</p>';
                return;
            }

            container.innerHTML = selectedItems.map(item => `
                <div class="selected-item">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span style="font-size: 1.5rem;">${item.icon}</span>
                        <div>
                            <div style="font-weight: 600;">${item.name}</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">${item.category} (ID: ${item.id})</div>
                        </div>
                    </div>
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">-</button>
                        <input type="number" class="quantity-input" value="${item.quantity}" min="1" 
                               onchange="updateQuantity(${item.id}, this.value)">
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">+</button>
                        <button class="remove-btn" onclick="removeItem(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function generateOutput() {
            const output = selectedItems.map(item => 
                `${item.id},${item.quantity},1,0,0,0,0`
            ).join('\n');
            
            document.getElementById('output').value = output;
            showStatus(`📝 Generated output for ${selectedItems.length} items`);
        }

        function addRandomItems() {
            const itemIds = Object.keys(testItems);
            const randomIds = itemIds.sort(() => 0.5 - Math.random()).slice(0, 3);
            
            randomIds.forEach(id => addItem(parseInt(id)));
            showStatus(`🎲 Added ${randomIds.length} random items`);
        }

        function clearAllItems() {
            selectedItems = [];
            updateSelectedDisplay();
            document.getElementById('output').value = '';
            showStatus(`🗑️ Cleared all items`);
        }

        function testQuantities() {
            if (selectedItems.length === 0) {
                showStatus('⚠️ No items to test quantities', 'error');
                return;
            }

            selectedItems.forEach(item => {
                item.quantity = Math.floor(Math.random() * 10) + 1;
            });
            
            updateSelectedDisplay();
            showStatus(`🔢 Randomized quantities for ${selectedItems.length} items`);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initializing test page...');
            loadItems();
            showStatus('🎮 Test page loaded successfully!');
        });
    </script>
</body>
</html>
