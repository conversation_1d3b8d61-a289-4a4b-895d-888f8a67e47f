<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Giftcode UI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .item-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .item-card {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .selected-items {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Giftcode UI Functionality</h1>
        
        <div class="test-section">
            <div class="test-title">1. Test Item Loading</div>
            <button onclick="testItemLoading()">Test Load Items</button>
            <div id="item-loading-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. Test Item Display</div>
            <button onclick="testItemDisplay()">Test Display Items</button>
            <div id="item-display-result"></div>
            <div id="test-item-grid" class="item-grid"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. Test Item Selection</div>
            <button onclick="testItemSelection()">Test Add Items</button>
            <button onclick="testQuantityUpdate()">Test Quantity Update</button>
            <button onclick="testItemRemoval()">Test Remove Items</button>
            <div id="item-selection-result"></div>
            <div id="test-selected-items" class="selected-items"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. Test Search & Filter</div>
            <input type="text" id="test-search" placeholder="Search items..." style="padding: 8px; margin: 5px;">
            <select id="test-category" style="padding: 8px; margin: 5px;">
                <option value="">All Categories</option>
                <option value="Jewels">Jewels</option>
                <option value="Wings">Wings</option>
                <option value="Currency">Currency</option>
            </select>
            <button onclick="testSearchFilter()">Test Search & Filter</button>
            <div id="search-filter-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">5. Test Output Format</div>
            <button onclick="testOutputFormat()">Test Generate Output</button>
            <div id="output-format-result"></div>
            <textarea id="test-output" rows="5" style="width: 100%; margin: 10px 0;" readonly></textarea>
        </div>
    </div>

    <script>
        // Mock data for testing
        let testItems = {
            14: { name: 'Jewel of Bless', category: 'Jewels', icon: '💎' },
            15: { name: 'Jewel of Soul', category: 'Jewels', icon: '💠' },
            16: { name: 'Jewel of Life', category: 'Jewels', icon: '🔮' },
            260: { name: 'Wings of Elf', category: 'Wings', icon: '🪶' },
            700: { name: 'Bound Diamond', category: 'Currency', icon: '💎' }
        };
        
        let testSelectedItems = [];

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        function testItemLoading() {
            try {
                const itemCount = Object.keys(testItems).length;
                showResult('item-loading-result', 
                    `✅ Successfully loaded ${itemCount} test items`, 'success');
            } catch (error) {
                showResult('item-loading-result', 
                    `❌ Error loading items: ${error.message}`, 'error');
            }
        }

        function testItemDisplay() {
            try {
                const grid = document.getElementById('test-item-grid');
                grid.innerHTML = '';
                
                Object.entries(testItems).forEach(([id, item]) => {
                    const card = document.createElement('div');
                    card.className = 'item-card';
                    card.innerHTML = `
                        <div>${item.icon} ${item.name}</div>
                        <div>ID: ${id}</div>
                        <div>Category: ${item.category}</div>
                        <button onclick="addTestItem(${id})">Add</button>
                    `;
                    grid.appendChild(card);
                });
                
                showResult('item-display-result', 
                    `✅ Successfully displayed ${Object.keys(testItems).length} items`, 'success');
            } catch (error) {
                showResult('item-display-result', 
                    `❌ Error displaying items: ${error.message}`, 'error');
            }
        }

        function addTestItem(itemId) {
            const item = testItems[itemId];
            if (!item) return;

            const existingIndex = testSelectedItems.findIndex(selected => selected.id == itemId);
            if (existingIndex !== -1) {
                testSelectedItems[existingIndex].quantity += 1;
            } else {
                testSelectedItems.push({
                    id: itemId,
                    name: item.name,
                    icon: item.icon,
                    category: item.category,
                    quantity: 1
                });
            }
            updateTestSelectedDisplay();
        }

        function updateTestSelectedDisplay() {
            const container = document.getElementById('test-selected-items');
            if (testSelectedItems.length === 0) {
                container.innerHTML = '<p>No items selected</p>';
                return;
            }

            container.innerHTML = `
                <h4>Selected Items (${testSelectedItems.length}):</h4>
                ${testSelectedItems.map(item => `
                    <div style="display: flex; align-items: center; gap: 10px; margin: 5px 0; padding: 5px; border: 1px solid #ccc; border-radius: 3px;">
                        <span>${item.icon} ${item.name}</span>
                        <input type="number" value="${item.quantity}" min="1" onchange="updateTestQuantity(${item.id}, this.value)" style="width: 60px;">
                        <button onclick="removeTestItem(${item.id})" style="background: #dc3545;">Remove</button>
                    </div>
                `).join('')}
            `;
        }

        function updateTestQuantity(itemId, quantity) {
            const index = testSelectedItems.findIndex(item => item.id == itemId);
            if (index !== -1) {
                testSelectedItems[index].quantity = Math.max(1, parseInt(quantity) || 1);
                updateTestSelectedDisplay();
            }
        }

        function removeTestItem(itemId) {
            testSelectedItems = testSelectedItems.filter(item => item.id != itemId);
            updateTestSelectedDisplay();
        }

        function testItemSelection() {
            try {
                // Add some test items
                addTestItem(14);
                addTestItem(15);
                addTestItem(260);
                
                showResult('item-selection-result', 
                    `✅ Successfully added test items. Selected: ${testSelectedItems.length} items`, 'success');
            } catch (error) {
                showResult('item-selection-result', 
                    `❌ Error selecting items: ${error.message}`, 'error');
            }
        }

        function testQuantityUpdate() {
            try {
                if (testSelectedItems.length > 0) {
                    updateTestQuantity(testSelectedItems[0].id, 5);
                    showResult('item-selection-result', 
                        `✅ Successfully updated quantity for ${testSelectedItems[0].name}`, 'success');
                } else {
                    showResult('item-selection-result', 
                        `⚠️ No items to update quantity`, 'info');
                }
            } catch (error) {
                showResult('item-selection-result', 
                    `❌ Error updating quantity: ${error.message}`, 'error');
            }
        }

        function testItemRemoval() {
            try {
                if (testSelectedItems.length > 0) {
                    const removedItem = testSelectedItems[0].name;
                    removeTestItem(testSelectedItems[0].id);
                    showResult('item-selection-result', 
                        `✅ Successfully removed ${removedItem}`, 'success');
                } else {
                    showResult('item-selection-result', 
                        `⚠️ No items to remove`, 'info');
                }
            } catch (error) {
                showResult('item-selection-result', 
                    `❌ Error removing item: ${error.message}`, 'error');
            }
        }

        function testSearchFilter() {
            try {
                const searchTerm = document.getElementById('test-search').value.toLowerCase();
                const category = document.getElementById('test-category').value;
                
                let filteredItems = Object.entries(testItems).filter(([id, item]) => {
                    const matchesSearch = !searchTerm || item.name.toLowerCase().includes(searchTerm);
                    const matchesCategory = !category || item.category === category;
                    return matchesSearch && matchesCategory;
                });
                
                showResult('search-filter-result', 
                    `✅ Search/Filter working. Found ${filteredItems.length} items matching criteria`, 'success');
            } catch (error) {
                showResult('search-filter-result', 
                    `❌ Error in search/filter: ${error.message}`, 'error');
            }
        }

        function testOutputFormat() {
            try {
                const output = testSelectedItems.map(item => 
                    `${item.id},${item.quantity},1,0,0,0,0`
                ).join('\n');
                
                document.getElementById('test-output').value = output;
                
                showResult('output-format-result', 
                    `✅ Successfully generated output format for ${testSelectedItems.length} items`, 'success');
            } catch (error) {
                showResult('output-format-result', 
                    `❌ Error generating output: ${error.message}`, 'error');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            showResult('item-loading-result', '🔄 Ready to test item loading', 'info');
            showResult('item-display-result', '🔄 Ready to test item display', 'info');
            showResult('item-selection-result', '🔄 Ready to test item selection', 'info');
            showResult('search-filter-result', '🔄 Ready to test search & filter', 'info');
            showResult('output-format-result', '🔄 Ready to test output format', 'info');
        });
    </script>
</body>
</html>
