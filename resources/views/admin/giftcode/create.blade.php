@extends('layouts.admin')

@section('title', 'Tạo Giftcode mới')

@section('content')
<div class="giftcode-create-container">
    <!-- Header Section -->
    <div class="page-header-modern">
        <div class="header-content">
            <div class="header-icon">
                <i class="fas fa-gift"></i>
            </div>
            <div class="header-text">
                <h1 class="page-title">Tạo Giftcode Mới</h1>
                <p class="page-subtitle">Tạo mã quà tặng cho người chơi MU Online</p>
            </div>
        </div>
        <div class="header-actions">
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-glass">
                <i class="fas fa-arrow-left"></i>
                <span>Quay lại</span>
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.giftcode.store') }}" class="giftcode-form">
        @csrf

        <!-- Basic Information Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h3 class="card-title">Thông tin cơ bản</h3>
            </div>
            <div class="card-body-modern">
                <div class="form-grid">
                    <div class="form-group-modern">
                        <label for="type" class="form-label-modern">
                            <i class="fas fa-tag"></i>
                            Loại Giftcode
                            <span class="required">*</span>
                        </label>
                        <select class="form-input-modern @error('type') is-invalid @enderror"
                                id="type"
                                name="type">
                            <option value="1" {{ old('type', 1) == 1 ? 'selected' : '' }}>Công khai - Ai cũng dùng được</option>
                            <option value="2" {{ old('type') == 2 ? 'selected' : '' }}>Riêng tư - Chỉ tài khoản cụ thể</option>
                            <option value="0" {{ old('type') == 0 ? 'selected' : '' }}>Theo nhân vật - Mỗi nhân vật dùng 1 lần</option>
                        </select>
                        @error('type')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group-modern">
                        <label for="limit" class="form-label-modern">
                            <i class="fas fa-users"></i>
                            Giới hạn sử dụng
                            <span class="required">*</span>
                        </label>
                        <input type="number"
                               class="form-input-modern @error('limit') is-invalid @enderror"
                               id="limit"
                               name="limit"
                               value="{{ old('limit', 0) }}"
                               min="0"
                               placeholder="0 = Không giới hạn">
                        @error('limit')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group-modern">
                        <label for="period" class="form-label-modern">
                            <i class="fas fa-clock"></i>
                            Thời hạn (ngày)
                            <span class="required">*</span>
                        </label>
                        <input type="number"
                               class="form-input-modern @error('period') is-invalid @enderror"
                               id="period"
                               name="period"
                               value="{{ old('period', 0) }}"
                               min="0"
                               placeholder="0 = Không hết hạn">
                        @error('period')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group-modern">
                        <label for="zoneid" class="form-label-modern">
                            <i class="fas fa-server"></i>
                            Server áp dụng
                            <span class="required">*</span>
                        </label>
                        <select class="form-input-modern @error('zoneid') is-invalid @enderror"
                                id="zoneid"
                                name="zoneid">
                            <option value="0" {{ old('zoneid', 0) == 0 ? 'selected' : '' }}>Tất cả server</option>
                            <option value="1" {{ old('zoneid') == 1 ? 'selected' : '' }}>Server 1</option>
                            <option value="2" {{ old('zoneid') == 2 ? 'selected' : '' }}>Server 2</option>
                        </select>
                        @error('zoneid')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-group-modern full-width">
                    <label for="content" class="form-label-modern">
                        <i class="fas fa-align-left"></i>
                        Nội dung giftcode
                        <span class="required">*</span>
                    </label>
                    <textarea class="form-textarea-modern @error('content') is-invalid @enderror"
                              id="content"
                              name="content"
                              rows="3"
                              placeholder="Nội dung mô tả giftcode này...">{{ old('content') }}</textarea>
                    @error('content')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Accounts field for private type -->
                <div class="form-group-modern full-width" id="accounts_section" style="display: none;">
                    <label for="accounts" class="form-label-modern">
                        <i class="fas fa-users"></i>
                        Danh sách tài khoản được phép
                        <span class="required">*</span>
                    </label>
                    <textarea class="form-textarea-modern @error('accounts') is-invalid @enderror"
                              id="accounts"
                              name="accounts"
                              rows="3"
                              placeholder="Nhập tên tài khoản, cách nhau bằng dấu phẩy. Ví dụ: user1,user2,user3">{{ old('accounts') }}</textarea>
                    @error('accounts')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Code Generation Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-code"></i>
                </div>
                <h3 class="card-title">Tạo mã Code</h3>
            </div>
            <div class="card-body-modern">
                <div class="radio-group-modern">
                    <label class="form-label-modern">
                        <i class="fas fa-cogs"></i>
                        Loại tạo code
                        <span class="required">*</span>
                    </label>
                    <div class="radio-options">
                        <div class="radio-option">
                            <input type="radio"
                                   name="multiple"
                                   id="single_code"
                                   value="0"
                                   {{ old('multiple', '0') == '0' ? 'checked' : '' }}>
                            <label for="single_code" class="radio-label">
                                <div class="radio-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Code duy nhất</span>
                                    <span class="radio-desc">Tạo một mã code đơn lẻ</span>
                                </div>
                            </label>
                        </div>
                        <div class="radio-option">
                            <input type="radio"
                                   name="multiple"
                                   id="multiple_codes"
                                   value="1"
                                   {{ old('multiple') == '1' ? 'checked' : '' }}>
                            <label for="multiple_codes" class="radio-label">
                                <div class="radio-icon">
                                    <i class="fas fa-copy"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Nhiều code</span>
                                    <span class="radio-desc">Tạo nhiều mã code cùng lúc</span>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Single Code Section -->
                <div id="single_code_section" class="code-section">
                    <div class="form-group-modern">
                        <label for="code" class="form-label-modern">
                            <i class="fas fa-key"></i>
                            Mã Code
                            <span class="required">*</span>
                        </label>
                        <input type="text"
                               class="form-input-modern @error('code') is-invalid @enderror"
                               id="code"
                               name="code"
                               value="{{ old('code') }}"
                               placeholder="Ví dụ: MUTET2025">
                        @error('code')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Multiple Codes Section -->
                <div id="multiple_codes_section" class="code-section" style="display: none;">
                    <div class="form-group-modern">
                        <label for="number" class="form-label-modern">
                            <i class="fas fa-sort-numeric-up"></i>
                            Số lượng Code
                            <span class="required">*</span>
                        </label>
                        <input type="number"
                               class="form-input-modern @error('number') is-invalid @enderror"
                               id="number"
                               name="number"
                               value="{{ old('number', 10) }}"
                               min="1"
                               max="1000"
                               placeholder="100">
                        <small class="form-hint">Hệ thống sẽ tự động tạo mã code ngẫu nhiên</small>
                        @error('number')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Items Reward Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <h3 class="card-title">Vật phẩm thưởng</h3>
                <div class="card-actions">
                    <button type="button" class="btn btn-sm btn-primary" id="item_selector_btn" onclick="toggleItemSelector()">
                        <i class="fas fa-mouse-pointer"></i>
                        Chọn vật phẩm
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="manual_input_btn" onclick="toggleManualInput()">
                        <i class="fas fa-keyboard"></i>
                        Nhập thủ công
                    </button>
                </div>
            </div>
            <div class="card-body-modern">
                <!-- Item Selector Mode -->
                <div id="item_selector_mode" class="item-selector-container">
                    <!-- Search Section -->
                    <div class="search-section">
                        <div class="search-controls">
                            <input type="text" id="item_search" class="search-input" placeholder="🔍 Tìm kiếm vật phẩm...">
                            <select id="category_filter" class="category-select">
                                <option value="">Tất cả danh mục</option>
                                <option value="Jewels">💎 Jewels</option>
                                <option value="Wings">🪶 Wings</option>
                                <option value="Potions">🧪 Potions</option>
                                <option value="Currency">💰 Currency</option>
                            </select>
                            <button type="button" class="clear-btn" onclick="GiftcodeUI.clearFilters()">Clear</button>
                        </div>
                    </div>

                    <!-- Items Grid -->
                    <div class="items-section">
                        <h4 class="items-title">
                            🎁 Chọn vật phẩm (<span id="items_count">0</span>)
                        </h4>
                        <div class="items-grid" id="items_grid">
                            <!-- Items will be loaded here -->
                        </div>
                    </div>

                    <!-- Selected Items -->
                    <div class="selected-section">
                        <h4 class="selected-title">
                            🛒 Đã chọn (<span id="selected_count">0</span>)
                            <button type="button" class="clear-all-btn" onclick="GiftcodeUI.clearAll()">Xóa tất cả</button>
                        </h4>
                        <div class="selected-list" id="selected_list">
                            <div class="empty-message">
                                <i class="fas fa-inbox"></i>
                                <p>Chưa chọn vật phẩm nào</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manual Input Mode -->
                <div id="manual_input_mode" class="manual-input-container" style="display: none;">
                    <div class="form-group-modern full-width">
                        <label for="items" class="form-label-modern">
                            <i class="fas fa-gem"></i>
                            Danh sách vật phẩm
                            <span class="required">*</span>
                        </label>
                        <textarea class="form-textarea-modern @error('items') is-invalid @enderror"
                                  id="items"
                                  name="items"
                                  rows="8"
                                  placeholder="Mỗi dòng một vật phẩm theo format: goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo&#10;Ví dụ:&#10;14,5,1,0,0,0,0&#10;15,10,1,0,0,0,0&#10;16,1,1,0,0,0,0">{{ old('items') }}</textarea>
                        <small class="form-hint">
                            <strong>Format:</strong> goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo<br>
                            <strong>Ví dụ:</strong><br>
                            • 14,5,1,0,0,0,0 (5 Jewel of Bless)<br>
                            • 15,10,1,0,0,0,0 (10 Jewel of Soul)<br>
                            • 16,1,1,0,0,0,0 (1 Jewel of Life)
                        </small>
                        @error('items')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>



        <!-- Submit Actions -->
        <div class="form-actions">
            <button type="submit" class="btn btn-primary-modern">
                <i class="fas fa-magic"></i>
                <span>Tạo Giftcode</span>
            </button>
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-secondary-modern">
                <i class="fas fa-times"></i>
                <span>Hủy bỏ</span>
            </a>
        </div>
    </form>
</div>

<style>
/* Simple Item Selector Styles */
.item-selector-container {
    background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 100%);
    border-radius: 15px;
    padding: 2rem;
    border: 2px solid #cbd5e0;
}

.search-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.search-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-input, .category-select {
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #2d3748;
    font-weight: 500;
    flex: 1;
    min-width: 200px;
}

.search-input:focus, .category-select:focus {
    outline: none;
    border-color: #63b3ed;
    background: white;
    box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.2);
}

.clear-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
}

.clear-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.items-section, .selected-section {
    margin-bottom: 2rem;
}

.items-title, .selected-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(66, 153, 225, 0.3);
}

.clear-all-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.clear-all-btn:hover {
    background: #c53030;
    transform: scale(1.05);
}

.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    max-height: 500px;
    overflow-y: auto;
}

.item-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #cbd5e0;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.item-card:hover {
    border-color: #3182ce;
    background: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(49, 130, 206, 0.15);
}

.item-icon {
    font-size: 2.5rem;
    width: 4rem;
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    line-height: 1.2;
}

.item-category {
    font-size: 0.85rem;
    color: #718096;
    font-weight: 500;
}

.item-id {
    font-size: 0.75rem;
    color: #a0aec0;
    font-family: 'Courier New', monospace;
    background: #edf2f7;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
    margin-top: 0.25rem;
}

.add-btn {
    background: linear-gradient(135deg, #3182ce, #2c5282);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(49, 130, 206, 0.3);
    flex-shrink: 0;
}

.add-btn:hover {
    background: linear-gradient(135deg, #2c5282, #2a4365);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(49, 130, 206, 0.4);
}

.add-btn.added {
    background: linear-gradient(135deg, #38a169, #2f855a);
}

.selected-list {
    background: linear-gradient(135deg, #f0fff4 0%, #f7fafc 100%);
    border: 2px solid #c6f6d5;
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 200px;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.1);
}

.empty-message {
    text-align: center;
    color: #68d391;
    padding: 3rem 2rem;
}

.empty-message i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
    opacity: 0.7;
    color: #48bb78;
}

.empty-message p {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
}

.selected-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #f0fff4 100%);
    border: 2px solid #9ae6b4;
    border-radius: 12px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(72, 187, 120, 0.1);
    transition: all 0.2s ease;
}

.selected-item:hover {
    border-color: #68d391;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(72, 187, 120, 0.2);
}

.selected-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.selected-icon {
    font-size: 2rem;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.selected-details {
    flex: 1;
}

.selected-name {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.selected-category {
    font-size: 0.85rem;
    color: #718096;
    font-weight: 500;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: white;
    padding: 0.5rem;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.qty-btn {
    background: #3182ce;
    color: white;
    border: none;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
}

.qty-btn:hover {
    background: #2c5282;
    transform: scale(1.05);
}

.qty-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

.qty-input {
    width: 5rem;
    text-align: center;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    padding: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
}

.qty-input:focus {
    border-color: #3182ce;
    outline: none;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.remove-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
    margin-left: 0.5rem;
}

.remove-btn:hover {
    background: #c53030;
    transform: scale(1.05);
}

.search-filter-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    color: white;
}

.search-controls {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
    align-items: end;
}

.search-box {
    position: relative;
}

.search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #cbd5e0;
    pointer-events: none;
    font-size: 1.1rem;
}

.search-box input {
    padding-right: 3rem;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #2d3748;
    font-weight: 500;
}

.search-box input:focus {
    background: white;
    border-color: #63b3ed;
    box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.2);
}

.search-box input::placeholder {
    color: #a0aec0;
}

.filter-controls select {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #2d3748;
    font-weight: 500;
}

.filter-controls select:focus {
    background: white;
    border-color: #63b3ed;
    box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.2);
}

.filter-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.popular-items-section, .all-items-section, .selected-items-section {
    margin-bottom: 2rem;
}

.section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    margin-bottom: 1rem;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(66, 153, 225, 0.3);
}

.section-title .fas {
    margin-right: 0.5rem;
}

.selected-count, .items-count {
    background: #3182ce;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-left: auto;
}

.items-count {
    background: #718096;
}

.selected-actions {
    display: flex;
    gap: 0.5rem;
}

.popular-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
}

.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    max-height: 500px;
    overflow-y: auto;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
}

.item-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #cbd5e0;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.item-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3182ce, #63b3ed);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.item-card:hover {
    border-color: #3182ce;
    background: white;
    box-shadow: 0 8px 25px rgba(49, 130, 206, 0.15);
    transform: translateY(-3px);
}

.item-card:hover::before {
    transform: scaleX(1);
}

.item-card.popular-item {
    border-color: #f6ad55;
    background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
    box-shadow: 0 4px 12px rgba(246, 173, 85, 0.2);
}

.item-card.popular-item::before {
    background: linear-gradient(90deg, #f6ad55, #fbd38d);
}

.item-icon {
    font-size: 2.5rem;
    width: 4rem;
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    line-height: 1.2;
}

.item-category {
    font-size: 0.85rem;
    color: #718096;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.item-id {
    font-size: 0.75rem;
    color: #a0aec0;
    font-family: 'Courier New', monospace;
    background: #edf2f7;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
}

.add-item-btn {
    background: linear-gradient(135deg, #3182ce, #2c5282);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(49, 130, 206, 0.3);
    flex-shrink: 0;
}

.add-item-btn:hover {
    background: linear-gradient(135deg, #2c5282, #2a4365);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(49, 130, 206, 0.4);
}

.add-item-btn:active {
    transform: translateY(0);
}

.add-item-btn.added {
    background: linear-gradient(135deg, #38a169, #2f855a);
    cursor: default;
}

.add-item-btn.added:hover {
    background: linear-gradient(135deg, #38a169, #2f855a);
    transform: none;
}

.selected-items-list {
    min-height: 300px;
    background: linear-gradient(135deg, #f0fff4 0%, #f7fafc 100%);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #c6f6d5;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.1);
}

.empty-state {
    text-align: center;
    color: #68d391;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
    border: 2px dashed #9ae6b4;
    border-radius: 12px;
    margin: 1rem 0;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
    opacity: 0.7;
    color: #48bb78;
}

.empty-state p {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.empty-state small {
    font-size: 0.9rem;
    opacity: 0.8;
    color: #4a5568;
}

.selected-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #f0fff4 100%);
    border: 2px solid #9ae6b4;
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(72, 187, 120, 0.1);
}

.selected-item:hover {
    border-color: #68d391;
    box-shadow: 0 6px 20px rgba(72, 187, 120, 0.2);
    transform: translateY(-2px);
}

.selected-item-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.selected-item-icon {
    font-size: 2rem;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.selected-item-details {
    flex: 1;
    min-width: 0;
}

.selected-item-name {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.selected-item-category {
    font-size: 0.85rem;
    color: #718096;
    font-weight: 500;
}

.selected-item-id {
    font-size: 0.75rem;
    color: #a0aec0;
    font-family: 'Courier New', monospace;
    background: #edf2f7;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    display: inline-block;
    margin-top: 0.25rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: white;
    padding: 0.5rem;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.quantity-btn {
    background: #3182ce;
    color: white;
    border: none;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-weight: 600;
}

.quantity-btn:hover {
    background: #2c5282;
    transform: scale(1.05);
}

.quantity-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

.quantity-input {
    width: 5rem;
    text-align: center;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    padding: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.quantity-input:focus {
    border-color: #3182ce;
    outline: none;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.remove-item-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.2s ease;
    margin-left: 0.5rem;
}

.remove-item-btn:hover {
    background: #c53030;
    transform: scale(1.05);
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-outline-primary {
    border: 2px solid #3182ce;
    color: #3182ce;
    background: white;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-outline-primary:hover {
    background: #3182ce;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
}

.btn-outline-secondary, .btn-secondary {
    border: 2px solid #718096;
    color: white;
    background: #718096;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-outline-secondary:hover, .btn-secondary:hover {
    background: #4a5568;
    border-color: #4a5568;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(113, 128, 150, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
    border: 2px solid #3182ce;
    color: white;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2c5282 0%, #2a4365 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.4);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle type change to show/hide accounts section
    const typeSelect = document.getElementById('type');
    const accountsSection = document.getElementById('accounts_section');

    function toggleAccountsSection() {
        if (typeSelect.value == '2') { // Private type
            accountsSection.style.display = 'block';
        } else {
            accountsSection.style.display = 'none';
        }
    }

    typeSelect.addEventListener('change', toggleAccountsSection);
    toggleAccountsSection(); // Initial call

    // Handle code type change with smooth animations
    const codeTypeRadios = document.querySelectorAll('input[name="multiple"]');
    const singleCodeSection = document.getElementById('single_code_section');
    const multipleCodesSection = document.getElementById('multiple_codes_section');

    function toggleCodeSections() {
        const selectedType = document.querySelector('input[name="multiple"]:checked').value;

        // Add fade out effect
        singleCodeSection.style.opacity = '0';
        multipleCodesSection.style.opacity = '0';

        setTimeout(() => {
            if (selectedType === '0') { // Single code
                singleCodeSection.style.display = 'block';
                multipleCodesSection.style.display = 'none';
                setTimeout(() => singleCodeSection.style.opacity = '1', 50);
            } else { // Multiple codes
                singleCodeSection.style.display = 'none';
                multipleCodesSection.style.display = 'block';
                setTimeout(() => multipleCodesSection.style.opacity = '1', 50);
            }
        }, 150);
    }

    codeTypeRadios.forEach(radio => {
        radio.addEventListener('change', toggleCodeSections);
    });

    // Handle restriction type change
    const restrictionTypeRadios = document.querySelectorAll('input[name="restriction_type"]');
    const specificUsersSection = document.getElementById('specific_users_section');

    function toggleRestrictionSection() {
        if (document.querySelector('input[name="restriction_type"]:checked').value === 'specific_users') {
            specificUsersSection.style.display = 'block';
        } else {
            specificUsersSection.style.display = 'none';
        }
    }

    restrictionTypeRadios.forEach(radio => {
        radio.addEventListener('change', toggleRestrictionSection);
    });

    // Initial check on page load
    toggleRestrictionSection();

    // Handle reward type change with smooth animations
    const rewardTypeRadios = document.querySelectorAll('input[name="reward_type"]');
    const coinRewardSection = document.getElementById('coin_reward_section');
    const itemRewardSection = document.getElementById('item_reward_section');

    function toggleRewardSections() {
        const selectedType = document.querySelector('input[name="reward_type"]:checked').value;

        // Add fade out effect
        coinRewardSection.style.opacity = '0';
        itemRewardSection.style.opacity = '0';

        setTimeout(() => {
            const showCoin = selectedType === 'coins' || selectedType === 'mixed';
            const showItem = selectedType === 'items' || selectedType === 'mixed';

            coinRewardSection.style.display = showCoin ? 'block' : 'none';
            itemRewardSection.style.display = showItem ? 'block' : 'none';

            setTimeout(() => {
                if (showCoin) coinRewardSection.style.opacity = '1';
                if (showItem) itemRewardSection.style.opacity = '1';
            }, 50);
        }, 150);
    }

    rewardTypeRadios.forEach(radio => {
        radio.addEventListener('change', toggleRewardSections);
    });

    // Initialize sections
    toggleCodeSections();

    // Form validation feedback
    const form = document.querySelector('.giftcode-form');
    form.addEventListener('submit', function(e) {
        const submitBtn = form.querySelector('.btn-primary-modern');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Đang tạo...</span>';
        submitBtn.disabled = true;
    });

    // Initialize Giftcode UI
    GiftcodeUI.init();

    // Simple Giftcode UI Manager
    window.GiftcodeUI = {
        selectedItems: [],
        allItems: {},
        currentMode: 'selector',

        init: function() {
            console.log('🚀 Initializing Giftcode UI...');
            this.loadItems();
            this.setupEventListeners();
        },

        loadItems: function() {
            this.allItems = {
                14: { name: 'Jewel of Bless', category: 'Jewels', icon: '💎' },
                15: { name: 'Jewel of Soul', category: 'Jewels', icon: '💠' },
                16: { name: 'Jewel of Life', category: 'Jewels', icon: '🔮' },
                22: { name: 'Jewel of Creation', category: 'Jewels', icon: '✨' },
                31: { name: 'Jewel of Guardian', category: 'Jewels', icon: '🛡️' },
                260: { name: 'Wings of Elf', category: 'Wings', icon: '🪶' },
                261: { name: 'Wings of Heaven', category: 'Wings', icon: '🕊️' },
                262: { name: 'Wings of Satan', category: 'Wings', icon: '🦇' },
                700: { name: 'Bound Diamond', category: 'Currency', icon: '💎' },
                701: { name: 'Free Diamond', category: 'Currency', icon: '💠' },
                12: { name: 'Zen', category: 'Currency', icon: '💰' },
                0: { name: 'Apple', category: 'Potions', icon: '🍎' },
                1: { name: 'Small Healing Potion', category: 'Potions', icon: '🧪' },
                3: { name: 'Large Healing Potion', category: 'Potions', icon: '🧪' }
            };

            console.log('✅ Loaded', Object.keys(this.allItems).length, 'items');
            this.displayItems();
        },

        setupEventListeners: function() {
            const searchInput = document.getElementById('item_search');
            const categoryFilter = document.getElementById('category_filter');

            if (searchInput) {
                searchInput.addEventListener('input', () => this.filterItems());
            }
            if (categoryFilter) {
                categoryFilter.addEventListener('change', () => this.filterItems());
            }
        },

        displayItems: function() {
            const grid = document.getElementById('items_grid');
            const countElement = document.getElementById('items_count');

            if (!grid) {
                console.error('❌ Items grid not found!');
                return;
            }

            grid.innerHTML = '';
            const items = Object.entries(this.allItems);

            if (countElement) {
                countElement.textContent = items.length;
            }

            items.forEach(([id, item]) => {
                const card = this.createItemCard(id, item);
                grid.appendChild(card);
            });

            console.log('✅ Displayed', items.length, 'items');
        },

        createItemCard: function(id, item) {
            const isSelected = this.selectedItems.some(selected => selected.id == id);

            const card = document.createElement('div');
            card.className = 'item-card';
            card.innerHTML = `
                <div class="item-icon">${item.icon}</div>
                <div class="item-info">
                    <div class="item-name">${item.name}</div>
                    <div class="item-category">${item.category}</div>
                    <div class="item-id">ID: ${id}</div>
                </div>
                <button type="button" class="add-btn ${isSelected ? 'added' : ''}" onclick="GiftcodeUI.addItem(${id})">
                    <i class="fas ${isSelected ? 'fa-check' : 'fa-plus'}"></i>
                    ${isSelected ? 'Đã thêm' : 'Thêm'}
                </button>
            `;

            return card;
        },

        addItem: function(itemId) {
            const item = this.allItems[itemId];
            if (!item) return;

            const existingIndex = this.selectedItems.findIndex(selected => selected.id == itemId);
            if (existingIndex !== -1) {
                this.selectedItems[existingIndex].quantity += 1;
            } else {
                this.selectedItems.push({
                    id: itemId,
                    name: item.name,
                    icon: item.icon,
                    category: item.category,
                    quantity: 1
                });
            }

            this.updateSelectedDisplay();
            this.updateHiddenTextarea();
            this.displayItems(); // Refresh to show selected state
        },

        removeItem: function(itemId) {
            this.selectedItems = this.selectedItems.filter(item => item.id != itemId);
            this.updateSelectedDisplay();
            this.updateHiddenTextarea();
            this.displayItems(); // Refresh to show unselected state
        },

        updateQuantity: function(itemId, quantity) {
            const index = this.selectedItems.findIndex(item => item.id == itemId);
            if (index !== -1) {
                this.selectedItems[index].quantity = Math.max(1, parseInt(quantity) || 1);
                this.updateSelectedDisplay();
                this.updateHiddenTextarea();
            }
        },

        updateSelectedDisplay: function() {
            const container = document.getElementById('selected_list');
            const countElement = document.getElementById('selected_count');

            if (!container) return;

            if (countElement) {
                countElement.textContent = this.selectedItems.length;
            }

            if (this.selectedItems.length === 0) {
                container.innerHTML = `
                    <div class="empty-message">
                        <i class="fas fa-inbox"></i>
                        <p>Chưa chọn vật phẩm nào</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = this.selectedItems.map(item => `
                <div class="selected-item">
                    <div class="selected-info">
                        <div class="selected-icon">${item.icon}</div>
                        <div class="selected-details">
                            <div class="selected-name">${item.name}</div>
                            <div class="selected-category">${item.category} (ID: ${item.id})</div>
                        </div>
                    </div>
                    <div class="quantity-controls">
                        <button type="button" class="qty-btn" ${item.quantity <= 1 ? 'disabled' : ''} onclick="GiftcodeUI.updateQuantity(${item.id}, ${item.quantity - 1})">-</button>
                        <input type="number" class="qty-input" value="${item.quantity}" min="1" max="999" onchange="GiftcodeUI.updateQuantity(${item.id}, this.value)">
                        <button type="button" class="qty-btn" onclick="GiftcodeUI.updateQuantity(${item.id}, ${item.quantity + 1})">+</button>
                        <button type="button" class="remove-btn" onclick="GiftcodeUI.removeItem(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        },

        updateHiddenTextarea: function() {
            const textarea = document.getElementById('items');
            if (textarea && this.currentMode === 'selector') {
                const itemsText = this.selectedItems.map(item =>
                    `${item.id},${item.quantity},1,0,0,0,0`
                ).join('\n');
                textarea.value = itemsText;
            }
        },

        filterItems: function() {
            const searchTerm = document.getElementById('item_search')?.value.toLowerCase() || '';
            const selectedCategory = document.getElementById('category_filter')?.value || '';

            const grid = document.getElementById('items_grid');
            if (!grid) return;

            grid.innerHTML = '';
            let filteredCount = 0;

            Object.entries(this.allItems).forEach(([id, item]) => {
                const matchesSearch = !searchTerm ||
                    item.name.toLowerCase().includes(searchTerm) ||
                    id.includes(searchTerm);
                const matchesCategory = !selectedCategory || item.category === selectedCategory;

                if (matchesSearch && matchesCategory) {
                    const card = this.createItemCard(id, item);
                    grid.appendChild(card);
                    filteredCount++;
                }
            });

            const countElement = document.getElementById('items_count');
            if (countElement) {
                countElement.textContent = filteredCount;
            }
        },

        clearFilters: function() {
            const searchInput = document.getElementById('item_search');
            const categoryFilter = document.getElementById('category_filter');

            if (searchInput) searchInput.value = '';
            if (categoryFilter) categoryFilter.value = '';

            this.displayItems();
        },

        clearAll: function() {
            if (this.selectedItems.length === 0) return;

            if (confirm('Bạn có chắc muốn xóa tất cả vật phẩm đã chọn?')) {
                this.selectedItems = [];
                this.updateSelectedDisplay();
                this.updateHiddenTextarea();
                this.displayItems();
            }
        }
    };

    // Toggle functions for backward compatibility
    window.toggleItemSelector = function() {
        GiftcodeUI.currentMode = 'selector';
        document.getElementById('item_selector_mode').style.display = 'block';
        document.getElementById('manual_input_mode').style.display = 'none';

        const selectorBtn = document.getElementById('item_selector_btn');
        const manualBtn = document.getElementById('manual_input_btn');

        if (selectorBtn) selectorBtn.className = 'btn btn-sm btn-primary';
        if (manualBtn) manualBtn.className = 'btn btn-sm btn-outline-secondary';
    };

    window.toggleManualInput = function() {
        GiftcodeUI.currentMode = 'manual';
        document.getElementById('item_selector_mode').style.display = 'none';
        document.getElementById('manual_input_mode').style.display = 'block';

        const selectorBtn = document.getElementById('item_selector_btn');
        const manualBtn = document.getElementById('manual_input_btn');

        if (selectorBtn) selectorBtn.className = 'btn btn-sm btn-outline-primary';
        if (manualBtn) manualBtn.className = 'btn btn-sm btn-secondary';
    };

    // Initialize sections
    toggleCodeSections();
});
</script>

<style>
/* Modern Giftcode Creation Styles */
.giftcode-create-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

/* Page Header */
.page-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
    border: 1px solid rgba(147, 51, 234, 0.2);
    border-radius: 20px;
    backdrop-filter: blur(20px);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.3);
}

.header-text h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-text p {
    margin: 5px 0 0 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.header-actions .btn-glass {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 20px;
    border-radius: 12px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.header-actions .btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Form Cards */
.form-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    margin-bottom: 25px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-header-modern {
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.card-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
}

.card-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: white;
}

.card-body-modern {
    padding: 25px;
}

/* Form Groups */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group-modern {
    margin-bottom: 20px;
}

.form-group-modern.full-width {
    grid-column: 1 / -1;
}

.form-label-modern {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    font-weight: 600;
    color: white;
    font-size: 14px;
}

.form-label-modern i {
    color: #9333ea;
    width: 16px;
}

.required {
    color: #ef4444;
    font-weight: bold;
}

/* Form Inputs */
.form-input-modern, .form-textarea-modern {
    width: 100%;
    padding: 15px 18px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-input-modern:focus, .form-textarea-modern:focus {
    outline: none;
    border-color: #9333ea;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 4px rgba(147, 51, 234, 0.1);
}

.form-input-modern::placeholder, .form-textarea-modern::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.coin-input {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
    border-color: rgba(251, 191, 36, 0.3);
}

.item-textarea {
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border-color: rgba(34, 197, 94, 0.3);
}

.form-hint {
    display: block;
    margin-top: 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
}

.error-message {
    margin-top: 8px;
    color: #ef4444;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.error-message::before {
    content: "⚠️";
    font-size: 14px;
}

/* Radio Groups */
.radio-group-modern {
    margin-bottom: 25px;
}

.radio-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.radio-option {
    position: relative;
}

.radio-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.radio-label:hover {
    border-color: rgba(147, 51, 234, 0.5);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.radio-option input[type="radio"]:checked + .radio-label {
    border-color: #9333ea;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.2);
}

.radio-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.radio-icon.coin-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.radio-icon.item-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.radio-icon.mixed-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.radio-content {
    flex: 1;
}

.radio-title {
    display: block;
    font-weight: 600;
    color: white;
    font-size: 16px;
    margin-bottom: 4px;
}

.radio-desc {
    display: block;
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
}

/* Code and Reward Sections */
.code-section, .reward-section {
    margin-top: 20px;
    transition: all 0.3s ease;
}

/* Item Format Guide */
.item-format-guide {
    margin-top: 15px;
    padding: 15px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.guide-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #60a5fa;
    margin-bottom: 8px;
    font-size: 14px;
}

.guide-content {
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    line-height: 1.5;
}

.guide-content code {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #60a5fa;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.btn-primary-modern {
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
}

.btn-primary-modern:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
}

.btn-primary-modern:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-secondary-modern {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 13px 28px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary-modern:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .giftcode-create-container {
        padding: 15px;
    }

    .page-header-modern {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .radio-options {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn-primary-modern, .btn-secondary-modern {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-text h1 {
        font-size: 24px;
    }

    .card-body-modern {
        padding: 20px;
    }

    .radio-label {
        padding: 15px;
    }

    .radio-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* Item Selection Styles */
.item-selection-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.item-selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.selection-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 10px;
}

.selection-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.search-input, .category-select {
    min-width: 200px;
    padding: 10px 15px;
    font-size: 14px;
}

.popular-items-section {
    margin-bottom: 30px;
}

.popular-title {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #fbbf24;
    display: flex;
    align-items: center;
    gap: 8px;
}

.popular-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.item-browser {
    margin-bottom: 30px;
}

.item-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.item-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.item-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: #9333ea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.2);
}

.item-card.popular-item {
    border-color: #fbbf24;
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
}

.item-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    flex-shrink: 0;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 600;
    color: white;
    font-size: 14px;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-category {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 2px;
}

.item-id {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    font-family: 'Courier New', monospace;
}

.add-item-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.add-item-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: scale(1.1);
}

.selected-items-section {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 25px;
}

.selected-title {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #10b981;
    display: flex;
    align-items: center;
    gap: 8px;
}

.selected-items-list {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    min-height: 100px;
}

.empty-selection {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    padding: 30px;
}

.empty-selection i {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
}

.selected-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.selected-item:last-child {
    margin-bottom: 0;
}

.selected-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.selected-item-icon {
    font-size: 20px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    flex-shrink: 0;
}

.selected-item-info {
    flex: 1;
    min-width: 0;
}

.selected-item-name {
    font-weight: 600;
    color: white;
    font-size: 14px;
    margin-bottom: 2px;
}

.selected-item-category {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 1px;
}

.selected-item-id {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    font-family: 'Courier New', monospace;
}

.selected-item-quantity {
    margin-right: 10px;
}

.quantity-input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-align: center;
    font-size: 14px;
}

.quantity-input:focus {
    outline: none;
    border-color: #9333ea;
    background: rgba(255, 255, 255, 0.15);
}

.remove-item-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.remove-item-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: scale(1.1);
}
</style>
@endsection
