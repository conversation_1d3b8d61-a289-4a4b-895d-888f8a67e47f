@extends('layouts.admin')

@section('title', '<PERSON><PERSON><PERSON><PERSON> lý Giftcode - MU Admin Panel')

@section('styles')
<style>
    .breadcrumb {
        color: white;
        margin-bottom: 20px;
        opacity: 0.8;
    }
    .breadcrumb a {
        color: white;
        text-decoration: none;
    }
    .breadcrumb a:hover {
        text-decoration: underline;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 30px;
        margin-bottom: 30px;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .page-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
    }
    .page-subtitle {
        opacity: 0.8;
        font-size: 16px;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px;
        color: white;
        text-align: center;
    }
    .stat-icon {
        font-size: 32px;
        margin-bottom: 15px;
    }
    .stat-value {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 5px;
    }
    .stat-label {
        opacity: 0.8;
        font-size: 14px;
    }
    .filter-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px;
        margin-bottom: 30px;
    }
    .filter-form {
        display: flex;
        gap: 15px;
        align-items: end;
        flex-wrap: wrap;
    }
    .form-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    .form-group label {
        color: white;
        font-weight: 500;
        font-size: 14px;
    }
    .form-control {
        padding: 12px 16px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 14px;
        min-width: 150px;
    }
    .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        background: rgba(255, 255, 255, 0.15);
    }
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        font-size: 14px;
    }
    .btn-primary {
        background: linear-gradient(45deg, #3b82f6, #2563eb);
        color: white;
    }
    .btn-success {
        background: linear-gradient(45deg, #10b981, #059669);
        color: white;
    }
    .btn-danger {
        background: linear-gradient(45deg, #ef4444, #dc2626);
        color: white;
    }
    .btn:hover {
        transform: translateY(-2px);
    }
    .giftcode-table {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }
    .table-header {
        background: rgba(255, 255, 255, 0.1);
        padding: 20px 25px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .table-title {
        color: white;
        font-size: 18px;
        font-weight: 600;
    }
    .table-responsive {
        overflow-x: auto;
        max-height: 70vh;
        overflow-y: auto;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        min-width: 1000px;
    }
    th, td {
        padding: 15px 20px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        white-space: nowrap;
    }
    th {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: sticky;
        top: 0;
        z-index: 10;
        backdrop-filter: blur(16px);
    }
    td {
        color: white;
        font-size: 14px;
    }
    tr:hover {
        background: rgba(255, 255, 255, 0.05);
    }
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .status-active {
        background: rgba(16, 185, 129, 0.2);
        color: #10b981;
        border: 1px solid rgba(16, 185, 129, 0.3);
    }
    .status-expired {
        background: rgba(239, 68, 68, 0.2);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.3);
    }
    .status-inactive {
        background: rgba(107, 114, 128, 0.2);
        color: #9ca3af;
        border: 1px solid rgba(107, 114, 128, 0.3);
    }
    .type-badge {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
    }
    .type-public {
        background: rgba(59, 130, 246, 0.2);
        color: #3b82f6;
        border: 1px solid rgba(59, 130, 246, 0.3);
    }
    .type-vip {
        background: rgba(139, 92, 246, 0.2);
        color: #8b5cf6;
        border: 1px solid rgba(139, 92, 246, 0.3);
    }
    .type-event {
        background: rgba(245, 158, 11, 0.2);
        color: #f59e0b;
        border: 1px solid rgba(245, 158, 11, 0.3);
    }
    .type-limited {
        background: rgba(239, 68, 68, 0.2);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.3);
    }
    .code-text {
        font-family: 'Courier New', monospace;
        background: rgba(255, 255, 255, 0.1);
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 600;
    }
    .progress-bar {
        width: 100px;
        height: 8px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        overflow: hidden;
    }
    .progress-fill {
        height: 100%;
        background: linear-gradient(45deg, #10b981, #059669);
        transition: width 0.3s ease;
    }
    .action-buttons {
        display: flex;
        gap: 8px;
    }
    .btn-sm {
        padding: 6px 12px;
        font-size: 12px;
    }
    .no-data {
        text-align: center;
        padding: 60px 20px;
        color: white;
        opacity: 0.7;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 20px;
            text-align: center;
        }
        .filter-form {
            flex-direction: column;
            align-items: stretch;
        }
        .form-control {
            min-width: auto;
            width: 100%;
        }
        .btn {
            width: 100%;
            margin-top: 10px;
        }
        .stats-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }
        th, td {
            padding: 10px 8px;
            font-size: 12px;
        }
        .action-buttons {
            flex-direction: column;
        }
    }
</style>
@endsection

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <a href="/admin/dashboard">Dashboard</a> /
        Quản lý Giftcode
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h1 class="page-title">🎁 Quản lý Giftcode</h1>
            <p class="page-subtitle">Tạo và quản lý các mã giftcode cho người chơi</p>
        </div>
        <a href="{{ route('admin.giftcode.create') }}" class="btn btn-success">➕ Tạo Giftcode mới</a>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-value">{{ number_format($stats['total_codes']) }}</div>
            <div class="stat-label">Tổng số giftcode</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">✅</div>
            <div class="stat-value">{{ number_format($stats['active_codes']) }}</div>
            <div class="stat-label">Đang hoạt động</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">⏰</div>
            <div class="stat-value">{{ number_format($stats['expired_codes']) }}</div>
            <div class="stat-label">Đã hết hạn</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">🎯</div>
            <div class="stat-value">{{ number_format($stats['total_usage']) }}</div>
            <div class="stat-label">Lượt sử dụng</div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <form method="GET" action="{{ route('admin.giftcode.index') }}" class="filter-form">
            <div class="form-group">
                <label>Tìm kiếm</label>
                <input type="text" name="search" class="form-control" placeholder="Mã code, tên..." value="{{ $search }}">
            </div>
            <div class="form-group">
                <label>Trạng thái</label>
                <select name="status" class="form-control">
                    <option value="all" {{ $statusFilter == 'all' ? 'selected' : '' }}>Tất cả</option>
                    <option value="active" {{ $statusFilter == 'active' ? 'selected' : '' }}>Hoạt động</option>
                    <option value="expired" {{ $statusFilter == 'expired' ? 'selected' : '' }}>Hết hạn</option>
                    <option value="inactive" {{ $statusFilter == 'inactive' ? 'selected' : '' }}>Vô hiệu hóa</option>
                </select>
            </div>
            <div class="form-group">
                <label>Loại</label>
                <select name="type" class="form-control">
                    <option value="all" {{ $typeFilter == 'all' ? 'selected' : '' }}>Tất cả</option>
                    <option value="1" {{ $typeFilter == '1' ? 'selected' : '' }}>Công khai</option>
                    <option value="2" {{ $typeFilter == '2' ? 'selected' : '' }}>Riêng tư</option>
                    <option value="0" {{ $typeFilter == '0' ? 'selected' : '' }}>Theo nhân vật</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">🔍 Tìm kiếm</button>
        </form>
    </div>

    <!-- Giftcode Table -->
    <div class="giftcode-table">
        <div class="table-header">
            <h3 class="table-title">📋 Danh sách Giftcode</h3>
            <a href="{{ route('admin.giftcode.usage-report') }}" class="btn btn-primary btn-sm">📊 Báo cáo sử dụng</a>
        </div>

        @if($giftcodes->count() > 0)
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Mã Code</th>
                            <th>Tên</th>
                            <th>Loại</th>
                            <th>Sử dụng</th>
                            <th>Tiến độ</th>
                            <th>Trạng thái</th>
                            <th>Hết hạn</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($giftcodes as $giftcode)
                            @php
                                $isExpired = $giftcode->period > 0 && \Carbon\Carbon::parse($giftcode->created_at)->addDays($giftcode->period)->isPast();
                                $isUsedUp = $giftcode->limit > 0 && $giftcode->usage_count >= $giftcode->limit;
                                $progressPercent = $giftcode->limit > 0 ? ($giftcode->usage_count / $giftcode->limit) * 100 : 0;

                                // Handle multiple codes display
                                $codeDisplay = '';
                                $codes = $giftcode->code; // This is always an array due to the model accessor

                                if (is_array($codes) && count($codes) > 0) {
                                    $codeDisplay = count($codes) > 1 ? $codes[0] . ' (+' . (count($codes)-1) . ' khác)' : $codes[0];
                                } else {
                                    $codeDisplay = 'N/A';
                                }

                                // Type display
                                $typeText = '';
                                $typeClass = '';
                                switch($giftcode->type) {
                                    case 1:
                                        $typeText = 'Công khai';
                                        $typeClass = 'public';
                                        break;
                                    case 2:
                                        $typeText = 'Riêng tư';
                                        $typeClass = 'vip';
                                        break;
                                    case 0:
                                        $typeText = 'Theo NV';
                                        $typeClass = 'event';
                                        break;
                                    default:
                                        $typeText = 'Khác';
                                        $typeClass = 'limited';
                                }
                            @endphp
                            <tr>
                                <td>{{ $giftcode->id }}</td>
                                <td>
                                    <span class="code-text">{{ $codeDisplay }}</span>
                                </td>
                                <td>{{ $giftcode->name ?: $giftcode->content }}</td>
                                <td>
                                    <span class="type-badge type-{{ $typeClass }}">
                                        {{ $typeText }}
                                    </span>
                                </td>
                                <td>{{ number_format($giftcode->usage_count) }}/{{ $giftcode->limit > 0 ? number_format($giftcode->limit) : '∞' }}</td>
                                <td>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: {{ $progressPercent }}%"></div>
                                    </div>
                                </td>
                                <td>
                                    @if(!$giftcode->is_active)
                                        <span class="status-badge status-inactive">Vô hiệu hóa</span>
                                    @elseif($isExpired)
                                        <span class="status-badge status-expired">Hết hạn</span>
                                    @elseif($isUsedUp)
                                        <span class="status-badge status-expired">Hết lượt</span>
                                    @else
                                        <span class="status-badge status-active">Hoạt động</span>
                                    @endif
                                </td>
                                <td>
                                    @if($giftcode->period > 0)
                                        {{ \Carbon\Carbon::parse($giftcode->created_at)->addDays($giftcode->period)->format('d/m/Y H:i') }}
                                    @else
                                        Không giới hạn
                                    @endif
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ route('admin.giftcode.show', $giftcode->id) }}" class="btn btn-primary btn-sm">👁️</a>
                                        <a href="{{ route('admin.giftcode.edit', $giftcode->id) }}" class="btn btn-primary btn-sm">✏️</a>
                                        <button onclick="deleteGiftcode({{ $giftcode->id }})" class="btn btn-danger btn-sm">🗑️</button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div style="padding: 20px 25px;">
                {{ $giftcodes->appends(request()->query())->links() }}
            </div>
        @else
            <div class="no-data">
                <h3>📋 Không có giftcode nào</h3>
                <p>Hãy tạo giftcode đầu tiên để bắt đầu.</p>
                <a href="{{ route('admin.giftcode.create') }}" class="btn btn-success" style="margin-top: 20px;">➕ Tạo Giftcode mới</a>
            </div>
        @endif
    </div>
</div>

<script>
function deleteGiftcode(id) {
    if (!confirm('Bạn có chắc chắn muốn xóa giftcode này?')) {
        return;
    }

    fetch(`/admin/giftcode/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Lỗi: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi xóa giftcode');
    });
}
</script>
@endsection
