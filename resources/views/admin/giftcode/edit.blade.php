@extends('layouts.admin')

@section('title', 'Chỉnh sửa Giftcode')

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <h1 class="page-title">✏️ Chỉnh sửa Giftcode</h1>
        <div class="page-actions">
            <a href="{{ route('admin.giftcode.show', $giftcode->id) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> Xem chi tiết
            </a>
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">📝 Thông tin Giftcode</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.giftcode.update', $giftcode->id) }}">
                @csrf
                @method('PUT')
                
                <!-- Basic Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label for="name" class="form-label">Tên Giftcode <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name', $giftcode->name) }}" 
                               placeholder="Ví dụ: Giftcode chào mừng năm mới">
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="max_uses" class="form-label">Số lần sử dụng tối đa <span class="text-danger">*</span></label>
                        <input type="number" class="form-control @error('max_uses') is-invalid @enderror" 
                               id="max_uses" name="max_uses" value="{{ old('max_uses', $giftcode->max_uses) }}" 
                               min="1" max="10000">
                        @error('max_uses')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-12">
                        <label for="description" class="form-label">Mô tả</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" 
                                  placeholder="Mô tả chi tiết về giftcode này...">{{ old('description', $giftcode->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Code Display (Read-only) -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">🔢 Mã Code (Không thể thay đổi)</h5>
                    </div>
                    <div class="card-body">
                        @php
                            $codes = is_array($giftcode->code) ? $giftcode->code : json_decode($giftcode->code, true);
                            if (!is_array($codes)) $codes = [$giftcode->code];
                        @endphp
                        
                        @if(count($codes) > 1)
                            <div class="alert alert-info">
                                <strong>Giftcode này có {{ count($codes) }} mã code:</strong>
                                <div class="codes-preview mt-2">
                                    @foreach(array_slice($codes, 0, 5) as $code)
                                        <span class="badge badge-secondary me-1">{{ $code }}</span>
                                    @endforeach
                                    @if(count($codes) > 5)
                                        <span class="text-muted">... và {{ count($codes) - 5 }} mã khác</span>
                                    @endif
                                </div>
                            </div>
                        @else
                            <div class="alert alert-info">
                                <strong>Mã code:</strong> 
                                <span class="badge badge-secondary">{{ $codes[0] ?? 'N/A' }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Rewards Display (Read-only) -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">🎁 Phần thưởng (Không thể thay đổi)</h5>
                    </div>
                    <div class="card-body">
                        @if($giftcode->rewards)
                            @php $rewards = is_array($giftcode->rewards) ? $giftcode->rewards : json_decode($giftcode->rewards, true); @endphp
                            
                            @if(isset($rewards['coins']) && $rewards['coins'] > 0)
                                <div class="reward-item">
                                    <i class="fas fa-coins text-warning"></i>
                                    <strong>{{ number_format($rewards['coins']) }} Coin</strong>
                                </div>
                            @endif

                            @if(isset($rewards['items']) && is_array($rewards['items']) && count($rewards['items']) > 0)
                                <div class="reward-section">
                                    <h6><i class="fas fa-box text-info"></i> Items:</h6>
                                    <div class="items-list">
                                        @foreach($rewards['items'] as $item)
                                            <div class="item-entry">
                                                <span class="item-name">{{ $item['name'] ?? 'Item' }}</span>
                                                <span class="item-details">
                                                    ID: {{ $item['id'] }}, 
                                                    Số lượng: {{ $item['quantity'] }}
                                                </span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        @else
                            <p class="text-muted">Không có thông tin phần thưởng</p>
                        @endif
                    </div>
                </div>

                <!-- Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">⚙️ Cài đặt</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="expires_at" class="form-label">Ngày hết hạn</label>
                                <input type="datetime-local" class="form-control @error('expires_at') is-invalid @enderror" 
                                       id="expires_at" name="expires_at" 
                                       value="{{ old('expires_at', $giftcode->expires_at ? \Carbon\Carbon::parse($giftcode->expires_at)->format('Y-m-d\TH:i') : '') }}">
                                <small class="form-text text-muted">Để trống nếu không có thời hạn</small>
                                @error('expires_at')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Trạng thái</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active" 
                                           value="1" {{ old('is_active', $giftcode->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Kích hoạt giftcode
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Stats -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">📊 Thống kê hiện tại</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-box">
                                    <div class="stat-value">{{ number_format($giftcode->used_count) }}</div>
                                    <div class="stat-label">Đã sử dụng</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box">
                                    @php $remaining = $giftcode->max_uses > 0 ? max(0, $giftcode->max_uses - $giftcode->used_count) : 'Không giới hạn'; @endphp
                                    <div class="stat-value">{{ is_numeric($remaining) ? number_format($remaining) : $remaining }}</div>
                                    <div class="stat-label">Còn lại</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box">
                                    @php $usagePercent = $giftcode->max_uses > 0 ? ($giftcode->used_count / $giftcode->max_uses) * 100 : 0; @endphp
                                    <div class="stat-value">{{ number_format($usagePercent, 1) }}%</div>
                                    <div class="stat-label">Tỷ lệ sử dụng</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box">
                                    <div class="stat-value">
                                        @if($giftcode->is_active)
                                            <span class="text-success">Hoạt động</span>
                                        @else
                                            <span class="text-danger">Vô hiệu</span>
                                        @endif
                                    </div>
                                    <div class="stat-label">Trạng thái</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit -->
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Cập nhật Giftcode
                        </button>
                        <a href="{{ route('admin.giftcode.show', $giftcode->id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Hủy
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.card {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

.reward-item {
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
}

.reward-section {
    margin-top: 15px;
}

.items-list {
    margin-top: 10px;
}

.item-entry {
    padding: 8px;
    margin-bottom: 5px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.item-name {
    font-weight: bold;
    color: #495057;
}

.item-details {
    font-size: 0.9em;
    color: #6c757d;
    margin-left: 10px;
}

.codes-preview .badge {
    margin-right: 5px;
    margin-bottom: 5px;
}

.stat-box {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
}

.text-danger {
    color: #dc3545 !important;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
</style>
@endsection
