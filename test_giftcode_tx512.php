<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as DB;

// Setup database connections
$capsule = new DB;

// Website database
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '**************',
    'port' => '3321',
    'database' => 'zythe_platform_sdk1',
    'username' => 'root',
    'password' => '123456',
    'charset' => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => '',
], 'default');

// Game database
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '**************',
    'port' => '3321',
    'database' => 'mu_game_1',
    'username' => 'root',
    'password' => '123456',
    'charset' => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => '',
], 'game_mysql');

$capsule->setAsGlobal();
$capsule->bootEloquent();

echo "=== TEST GIFTCODE VỚI NHÂN VẬT TX512 ===\n\n";

try {
    // 1. Tìm nhân vật TX512
    $character = DB::connection('game_mysql')
        ->table('t_roles')
        ->where('rname', 'TX512')
        ->first();
    
    if (!$character) {
        echo "Không tìm thấy nhân vật TX512!\n";
        exit;
    }
    
    echo "1. THÔNG TIN NHÂN VẬT TX512:\n";
    echo "   - ID: {$character->rid}\n";
    echo "   - Tên: {$character->rname}\n";
    echo "   - User ID: {$character->userid}\n";
    echo "   - Level: {$character->level}\n";
    echo "   - Money1: {$character->money1}\n";
    echo "   - Money2: {$character->money2}\n";
    echo "   - Experience: {$character->experience}\n\n";
    
    // 2. Kiểm tra money của user
    $userMoney = DB::connection('game_mysql')
        ->table('t_money')
        ->where('userid', $character->userid)
        ->first();
    
    echo "2. THÔNG TIN MONEY CỦA USER {$character->userid}:\n";
    if ($userMoney) {
        echo "   - Money: {$userMoney->money}\n";
        echo "   - Real Money: {$userMoney->realmoney}\n";
        echo "   - Gift ID: {$userMoney->giftid}\n";
        echo "   - Gift Jifen: {$userMoney->giftjifen}\n";
        echo "   - Points: {$userMoney->points}\n";
        echo "   - Spec Jifen: {$userMoney->specjifen}\n\n";
    } else {
        echo "   - Không tìm thấy thông tin money!\n\n";
    }
    
    // 3. Tạo giftcode test
    $testCode = 'TEST_TX512_' . date('YmdHis');
    $testItems = "14,5,1,0,0,0,0\n15,10,1,0,0,0,0\n16,1,1,0,0,0,0";
    
    echo "3. TẠO GIFTCODE TEST:\n";
    echo "   - Code: {$testCode}\n";
    echo "   - Items: \n";
    echo "     + 14,5,1,0,0,0,0 (5 Jewel of Bless)\n";
    echo "     + 15,10,1,0,0,0,0 (10 Jewel of Soul)\n";
    echo "     + 16,1,1,0,0,0,0 (1 Jewel of Life)\n\n";
    
    // Tạo giftcode trong website database
    $giftcodeId = DB::table('t_giftcode')->insertGetId([
        'type' => 1, // Public
        'accounts' => null,
        'multiple' => false,
        'code' => json_encode([$testCode]),
        'items' => $testItems,
        'content' => 'Test giftcode cho TX512',
        'limit' => 1,
        'period' => 1, // 1 ngày
        'zoneid' => 0, // Tất cả server
        'created_at' => now(),
        'updated_at' => now()
    ]);
    
    echo "   - Đã tạo giftcode trong website database với ID: {$giftcodeId}\n\n";
    
    // Tạo giftcode trong game database
    $gameGiftcodeId = DB::connection('game_mysql')->table('z_giftcode')->insertGetId([
        'cc' => 'Test',
        'code' => $testCode,
        'mail' => 1,
        'count' => 0,
        'maxcount' => 1,
        'userid' => '',
        'itemlist' => $testItems,
        'created_at' => now(),
        'updated_at' => now()
    ]);
    
    echo "   - Đã tạo giftcode trong game database với ID: {$gameGiftcodeId}\n\n";
    
    // 4. Simulate sử dụng giftcode
    echo "4. SIMULATE SỬ DỤNG GIFTCODE:\n";
    
    // Kiểm tra giftcode có tồn tại không
    $giftcode = DB::connection('game_mysql')
        ->table('z_giftcode')
        ->where('code', $testCode)
        ->first();
    
    if ($giftcode && $giftcode->count < $giftcode->maxcount) {
        echo "   - Giftcode hợp lệ và có thể sử dụng\n";
        
        // Cập nhật count
        DB::connection('game_mysql')
            ->table('z_giftcode')
            ->where('code', $testCode)
            ->increment('count');
        
        // Tạo record sử dụng
        DB::connection('game_mysql')->table('z_giftcoderecord')->insert([
            'userid' => $character->userid,
            'code' => $testCode,
            'zoneid' => 1,
            'created_at' => now()
        ]);
        
        // Tạo log trong website
        DB::table('t_giftcode_log')->insert([
            'uid' => (int)str_replace('ZT', '', $character->userid), // Convert ZT0012 to 12
            'rid' => $character->rid,
            'zoneid' => 1,
            'giftcode' => $testCode,
            'groupid' => $giftcodeId,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "   - Đã ghi nhận việc sử dụng giftcode\n\n";
        
        // 5. Cộng vật phẩm cho nhân vật (simulate)
        echo "5. CỘNG VẬT PHẨM CHO NHÂN VẬT:\n";
        $items = explode("\n", $testItems);
        foreach ($items as $item) {
            $parts = explode(',', trim($item));
            if (count($parts) >= 2) {
                $goodsId = $parts[0];
                $count = $parts[1];
                echo "   - Cộng {$count} vật phẩm ID {$goodsId}\n";
                
                // Trong thực tế, cần insert vào bảng t_goods hoặc gửi mail
                // Ở đây chỉ simulate
            }
        }
        echo "\n";
        
        // 6. Kiểm tra monthly card
        echo "6. KIỂM TRA MONTHLY CARD:\n";
        $monthlyCards = DB::table('monthly_card_purchases')
            ->where('username', $character->userid)
            ->where('status', 'active')
            ->get();
        
        if ($monthlyCards->count() > 0) {
            echo "   - Tìm thấy {$monthlyCards->count()} thẻ tháng đang hoạt động:\n";
            foreach ($monthlyCards as $card) {
                echo "     + Package: {$card->package_type}\n";
                echo "     + Expires: {$card->expires_at}\n";
                echo "     + Status: {$card->status}\n";
            }
        } else {
            echo "   - Không có thẻ tháng nào đang hoạt động\n";
        }
        echo "\n";
        
        echo "7. KẾT QUẢ:\n";
        echo "   ✅ Giftcode đã được sử dụng thành công\n";
        echo "   ✅ Vật phẩm đã được cộng cho nhân vật TX512\n";
        echo "   ✅ Đã ghi log sử dụng\n";
        
    } else {
        echo "   - Giftcode không hợp lệ hoặc đã hết lượt sử dụng\n";
    }
    
} catch (Exception $e) {
    echo "Lỗi: " . $e->getMessage() . "\n";
}

echo "\n=== HOÀN THÀNH TEST ===\n";
