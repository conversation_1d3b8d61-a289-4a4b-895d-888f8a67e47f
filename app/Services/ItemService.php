<?php

namespace App\Services;

class ItemService
{
    /**
     * Get list of common MU Online items
     * Format: [item_id => ['name' => 'Item Name', 'category' => 'Category', 'description' => 'Description']]
     */
    public static function getItemList()
    {
        return [
            // Jewels & Gems
            14 => [
                'name' => 'Jewel of Bless',
                'category' => 'Jewels',
                'description' => 'Tăng tỷ lệ thành công khi nâng cấp item',
                'icon' => '💎'
            ],
            15 => [
                'name' => 'Jewel of Soul',
                'category' => 'Jewels',
                'description' => 'Dùng để nâng cấp item',
                'icon' => '💠'
            ],
            16 => [
                'name' => 'Jewel of Life',
                'category' => 'Jewels',
                'description' => 'Tăng độ bền tối đa của item',
                'icon' => '🔮'
            ],
            22 => [
                'name' => 'Jewel of Creation',
                'category' => 'Jewels',
                'description' => 'Tạo ra socket cho item',
                'icon' => '✨'
            ],
            31 => [
                'name' => 'Jewel of Guardian',
                'category' => 'Jewels',
                'description' => 'Bảo vệ item khỏi bị hỏng khi nâng cấp',
                'icon' => '🛡️'
            ],
            41 => [
                'name' => 'Jewel of Harmony',
                'category' => 'Jewels',
                'description' => 'Tạo thuộc tính Harmony cho item',
                'icon' => '🌟'
            ],
            42 => [
                'name' => 'Lower Refining Stone',
                'category' => 'Jewels',
                'description' => 'Đá tinh luyện cấp thấp',
                'icon' => '🔸'
            ],
            43 => [
                'name' => 'Higher Refining Stone',
                'category' => 'Jewels',
                'description' => 'Đá tinh luyện cấp cao',
                'icon' => '🔹'
            ],

            // Potions
            0 => [
                'name' => 'Apple',
                'category' => 'Potions',
                'description' => 'Hồi phục HP nhỏ',
                'icon' => '🍎'
            ],
            1 => [
                'name' => 'Small Healing Potion',
                'category' => 'Potions',
                'description' => 'Hồi phục HP',
                'icon' => '🧪'
            ],
            2 => [
                'name' => 'Medium Healing Potion',
                'category' => 'Potions',
                'description' => 'Hồi phục HP trung bình',
                'icon' => '🧪'
            ],
            3 => [
                'name' => 'Large Healing Potion',
                'category' => 'Potions',
                'description' => 'Hồi phục HP lớn',
                'icon' => '🧪'
            ],
            4 => [
                'name' => 'Small Mana Potion',
                'category' => 'Potions',
                'description' => 'Hồi phục MP',
                'icon' => '🔵'
            ],
            5 => [
                'name' => 'Medium Mana Potion',
                'category' => 'Potions',
                'description' => 'Hồi phục MP trung bình',
                'icon' => '🔵'
            ],
            6 => [
                'name' => 'Large Mana Potion',
                'category' => 'Potions',
                'description' => 'Hồi phục MP lớn',
                'icon' => '🔵'
            ],

            // Scrolls
            7 => [
                'name' => 'Town Portal Scroll',
                'category' => 'Scrolls',
                'description' => 'Dịch chuyển về thành phố',
                'icon' => '📜'
            ],
            8 => [
                'name' => 'Antidote',
                'category' => 'Potions',
                'description' => 'Giải độc',
                'icon' => '💚'
            ],

            // Wings
            260 => [
                'name' => 'Wings of Elf',
                'category' => 'Wings',
                'description' => 'Cánh thiên thần cấp 1',
                'icon' => '🪶'
            ],
            261 => [
                'name' => 'Wings of Heaven',
                'category' => 'Wings',
                'description' => 'Cánh thiên đường cấp 1',
                'icon' => '🕊️'
            ],
            262 => [
                'name' => 'Wings of Satan',
                'category' => 'Wings',
                'description' => 'Cánh ác ma cấp 1',
                'icon' => '🦇'
            ],
            400 => [
                'name' => 'Wings of Mistery',
                'category' => 'Wings',
                'description' => 'Cánh bí ẩn cấp 2',
                'icon' => '🌙'
            ],
            401 => [
                'name' => 'Wings of Despair',
                'category' => 'Wings',
                'description' => 'Cánh tuyệt vọng cấp 2',
                'icon' => '⚡'
            ],

            // Zen & Special Items
            12 => [
                'name' => 'Zen',
                'category' => 'Currency',
                'description' => 'Tiền tệ trong game',
                'icon' => '💰'
            ],
            20 => [
                'name' => 'Remedy of Love',
                'category' => 'Special',
                'description' => 'Thuốc tình yêu',
                'icon' => '💖'
            ],
            21 => [
                'name' => 'Relic',
                'category' => 'Special',
                'description' => 'Di tích cổ',
                'icon' => '🏺'
            ],

            // Fruits
            13 => [
                'name' => 'Fruit',
                'category' => 'Fruits',
                'description' => 'Trái cây tăng điểm',
                'icon' => '🍇'
            ],

            // Boxes & Chests
            30 => [
                'name' => 'Excellent Item Box',
                'category' => 'Boxes',
                'description' => 'Hộp chứa item excellent ngẫu nhiên',
                'icon' => '📦'
            ],
            32 => [
                'name' => 'Chaos Box',
                'category' => 'Boxes',
                'description' => 'Hộp hỗn loạn để tạo item',
                'icon' => '🎁'
            ],

            // Feathers (for wing upgrades)
            48 => [
                'name' => 'Feather of Condor',
                'category' => 'Feathers',
                'description' => 'Lông chim để nâng cấp cánh',
                'icon' => '🪶'
            ],
            45 => [
                'name' => 'Flame of Condor',
                'category' => 'Feathers',
                'description' => 'Ngọn lửa chim để nâng cấp cánh',
                'icon' => '🔥'
            ],

            // Bound Items (Bound Diamonds)
            700 => [
                'name' => 'Bound Diamond',
                'category' => 'Currency',
                'description' => 'Kim cương ràng buộc (Bound Diamond)',
                'icon' => '💎'
            ],
            701 => [
                'name' => 'Free Diamond',
                'category' => 'Currency',
                'description' => 'Kim cương tự do',
                'icon' => '💠'
            ],

            // Upgrade Stones
            46 => [
                'name' => 'Upgrade Stone',
                'category' => 'Upgrade Materials',
                'description' => 'Đá nâng cấp',
                'icon' => '🗿'
            ],
            47 => [
                'name' => 'Elite Upgrade Stone',
                'category' => 'Upgrade Materials',
                'description' => 'Đá nâng cấp tinh anh',
                'icon' => '💎'
            ],

            // Event Items
            50 => [
                'name' => 'Christmas Star',
                'category' => 'Event Items',
                'description' => 'Ngôi sao Giáng sinh',
                'icon' => '⭐'
            ],
            51 => [
                'name' => 'Heart of Love',
                'category' => 'Event Items',
                'description' => 'Trái tim tình yêu',
                'icon' => '❤️'
            ],

            // Lucky Items
            52 => [
                'name' => 'Lucky Coin',
                'category' => 'Lucky Items',
                'description' => 'Đồng xu may mắn',
                'icon' => '🪙'
            ],
            53 => [
                'name' => 'Lucky Ticket',
                'category' => 'Lucky Items',
                'description' => 'Vé số may mắn',
                'icon' => '🎫'
            ]
        ];
    }

    /**
     * Get items grouped by category
     */
    public static function getItemsByCategory()
    {
        $items = self::getItemList();
        $grouped = [];

        foreach ($items as $id => $item) {
            $category = $item['category'];
            if (!isset($grouped[$category])) {
                $grouped[$category] = [];
            }
            $grouped[$category][$id] = $item;
        }

        return $grouped;
    }

    /**
     * Get item by ID
     */
    public static function getItemById($id)
    {
        $items = self::getItemList();
        return $items[$id] ?? null;
    }

    /**
     * Search items by name
     */
    public static function searchItems($query)
    {
        $items = self::getItemList();
        $results = [];

        foreach ($items as $id => $item) {
            if (stripos($item['name'], $query) !== false) {
                $results[$id] = $item;
            }
        }

        return $results;
    }

    /**
     * Get popular items for quick selection
     */
    public static function getPopularItems()
    {
        return [
            14, 15, 16, 22, 31, // Jewels
            260, 261, 262, // Wings
            700, 701, // Diamonds
            46, 47, // Upgrade stones
            3, 6, // Large potions
        ];
    }
}
