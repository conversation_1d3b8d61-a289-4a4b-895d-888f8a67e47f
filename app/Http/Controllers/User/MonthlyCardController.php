<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use App\Models\UserAccount;
use App\Models\MonthlyCardPurchase;
use App\Models\UserTransactionLog;
use App\Models\Account;
use App\Services\GameCacheService;
use Carbon\Carbon;

class MonthlyCardController extends Controller
{
    protected $gameCacheService;

    public function __construct(GameCacheService $gameCacheService)
    {
        $this->gameCacheService = $gameCacheService;
    }
    public function index()
    {
        $userSession = Session::get('user_account');
        if (!$userSession) {
            return redirect()->route('user.login');
        }

        $user = Account::find($userSession['id']);

        // Get user coins
        $userCoins = DB::table('user_coins')->where('account_id', $user->ID)->first();
        if (!$userCoins) {
            $userCoins = (object) ['coins' => 0, 'total_recharged' => 0, 'total_spent' => 0];
        }

        // Get active monthly cards
        $activeCards = DB::table('monthly_card_purchases')
            ->where('user_id', $user->ID)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->orderBy('expires_at', 'desc')
            ->get();

        // Get monthly card packages configuration
        $packages = $this->getMonthlyCardPackages();

        // Get purchase history
        $purchaseHistory = DB::table('monthly_card_purchases')
            ->where('user_id', $user->ID)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('user.monthly-card.index', compact(
            'user', 'userCoins', 'activeCards', 'packages', 'purchaseHistory'
        ));
    }

    /**
     * Get user's characters for monthly card selection
     */
    public function getCharacters(Request $request)
    {
        $userSession = Session::get('user_account');
        if (!$userSession) {
            return response()->json(['success' => false, 'message' => 'Vui lòng đăng nhập']);
        }

        $user = Account::find($userSession['id']);
        $gameUserId = 'ZT' . str_pad($user->ID, 4, '0', STR_PAD_LEFT);

        try {
            $characters = DB::connection('game_mysql')
                ->table('t_roles')
                ->where('userid', $gameUserId)
                ->where('isdel', 0)
                ->select('rid', 'rname', 'level', 'lasttime')
                ->orderBy('lasttime', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'characters' => $characters
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể tải danh sách nhân vật: ' . $e->getMessage()
            ]);
        }
    }

    public function purchase(Request $request)
    {
        $request->validate([
            'package_type' => 'required|in:premium',
            'character_id' => 'required|integer'
        ]);

        $userSession = Session::get('user_account');
        if (!$userSession) {
            return response()->json(['success' => false, 'message' => 'Vui lòng đăng nhập']);
        }

        $user = Account::find($userSession['id']);
        $packageType = $request->package_type;
        $characterId = $request->character_id;

        // Verify character belongs to this user
        $gameUserId = 'ZT' . str_pad($user->ID, 4, '0', STR_PAD_LEFT);
        $character = DB::connection('game_mysql')
            ->table('t_roles')
            ->where('rid', $characterId)
            ->where('userid', $gameUserId)
            ->where('isdel', 0)
            ->first();

        if (!$character) {
            return response()->json([
                'success' => false,
                'message' => 'Nhân vật không hợp lệ hoặc không thuộc về tài khoản của bạn'
            ]);
        }

        // Get package configuration
        $packages = $this->getMonthlyCardPackages();
        if (!isset($packages[$packageType])) {
            return response()->json(['success' => false, 'message' => 'Gói thẻ tháng không hợp lệ']);
        }

        $package = $packages[$packageType];

        // Check if user has enough coins
        $userCoins = DB::table('user_coins')->where('account_id', $user->ID)->first();
        if (!$userCoins || $userCoins->coins < $package['cost_coins']) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không đủ coin để mua gói này. Cần ' . number_format($package['cost_coins']) . ' coin.'
            ]);
        }

        // Check if user already has any active card (since we only have one package type now)
        $existingCard = DB::table('monthly_card_purchases')
            ->where('user_id', $user->ID)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->first();

        if ($existingCard) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn đã có thẻ tháng đang hoạt động. Vui lòng chờ hết hạn để mua thẻ mới.'
            ]);
        }

        DB::beginTransaction();
        try {
            // Step 1: Validation - Check if user has enough coins
            $userCoins = DB::table('user_coins')->where('account_id', $user->ID)->first();
            if (!$userCoins || $userCoins->coins < $package['cost_coins']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không đủ coin để mua thẻ tháng này. Cần ' . number_format($package['cost_coins']) . ' coin.'
                ]);
            }

            // Step 2: Deduct coins from user (Update user_coins - equivalent to zt_account.Money)
            $balanceBefore = $userCoins->coins;
            $balanceAfter = $balanceBefore - $package['cost_coins'];

            DB::table('user_coins')
                ->where('account_id', $user->ID)
                ->update([
                    'coins' => $balanceAfter,
                    'total_spent' => DB::raw('total_spent + ' . $package['cost_coins']),
                    'updated_at' => now()
                ]);

            // Create monthly card purchase record
            $activatedAt = now();
            $expiresAt = $activatedAt->copy()->addDays($package['duration_days']);

            $purchaseId = DB::table('monthly_card_purchases')->insertGetId([
                'user_id' => $user->ID,
                'package_name' => $package['name'],
                'package_type' => $packageType,
                'duration_days' => $package['duration_days'],
                'cost_coins' => $package['cost_coins'],
                'daily_reward_coins' => $package['daily_reward_coins'],
                'bonus_items' => json_encode($package['bonus_items'] ?? []),
                'daily_items' => json_encode($package['daily_items'] ?? []),
                'status' => 'active',
                'activated_at' => $activatedAt,
                'expires_at' => $expiresAt,
                'days_claimed' => 0,
                'ip_address' => $request->ip(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Step 3: Update game server database (t_tempmoney)
            $gameUserId = 'ZT' . str_pad($user->ID, 4, '0', STR_PAD_LEFT);

            DB::connection('game_mysql')->table('t_tempmoney')->insert([
                'uid' => $gameUserId,
                'addmoney' => $package['rmb_amount'] // RMB amount for game server
            ]);

            // Step 4: Log transaction in t_history (equivalent to old system)
            DB::table('t_history')->insert([
                'uid' => $user->ID,
                'rid' => $character->rid,
                'zoneid' => $character->zoneid ?? 1,
                'type' => 4, // 4 = monthly card transaction type
                'balance' => $balanceAfter,
                'content' => json_encode([
                    'money' => $package['cost_coins'],
                    'package_type' => $packageType,
                    'package_name' => $package['name'],
                    'rmb_amount' => $package['rmb_amount'],
                    'character_name' => $character->rname
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Step 5: Log the transaction in coin_spend_logs
            DB::table('coin_spend_logs')->insert([
                'account_id' => $user->ID,
                'username' => $user->UserName,
                'transaction_id' => 'MONTHLY_CARD_' . time() . '_' . rand(1000, 9999),
                'coins_spent' => $package['cost_coins'],
                'item_type' => 'monthly_card',
                'item_name' => $package['name'],
                'item_data' => json_encode([
                    'package_type' => $packageType,
                    'duration_days' => $package['duration_days'],
                    'purchase_id' => $purchaseId,
                    'rmb_amount' => $package['rmb_amount']
                ]),
                'description' => 'Mua thẻ tháng ' . $package['name'],
                'ip_address' => $request->ip(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Activate monthly card in game database (t_roleparams_char)
            try {
                // Get base value from idx=0 for this character
                $baseParam = DB::connection('game_mysql')
                    ->table('t_roleparams_char')
                    ->where('rid', $character->rid)
                    ->where('idx', 0)
                    ->first();

                $monthlyCardValue = '';
                if ($baseParam && $baseParam->v3) {
                    // Extract base value from v3 (format: "4959,xxxxx")
                    $v3Parts = explode(',', $baseParam->v3);
                    if (count($v3Parts) >= 1) {
                        $baseValue = intval($v3Parts[0]);
                        // Add 30 days for monthly card activation
                        $activatedValue = $baseValue + $package['duration_days'];
                        $monthlyCardValue = strval($activatedValue);

                        \Log::info('Monthly card activation calculation', [
                            'user_id' => $user->ID,
                            'character_rid' => $character->rid,
                            'character_name' => $character->rname,
                            'base_value' => $baseValue,
                            'duration_days' => $package['duration_days'],
                            'activated_value' => $activatedValue
                        ]);
                    }
                } else {
                    // Fallback: use default base value 4959 if no base param found
                    $baseValue = 4959;
                    $activatedValue = $baseValue + $package['duration_days'];
                    $monthlyCardValue = strval($activatedValue);

                    \Log::warning('No base param found for character, using default', [
                        'user_id' => $user->ID,
                        'character_rid' => $character->rid,
                        'character_name' => $character->rname,
                        'default_base' => $baseValue,
                        'activated_value' => $activatedValue
                    ]);
                }

                // Check if monthly card parameter already exists for this character
                $existingParam = DB::connection('game_mysql')
                    ->table('t_roleparams_char')
                    ->where('rid', $character->rid)
                    ->where('idx', 10)
                    ->first();

                if ($existingParam) {
                    // Update existing parameter
                    DB::connection('game_mysql')
                        ->table('t_roleparams_char')
                        ->where('rid', $character->rid)
                        ->where('idx', 10)
                        ->update([
                            'v3' => $monthlyCardValue  // Monthly card expiration value
                        ]);
                } else {
                    // Insert new parameter
                    DB::connection('game_mysql')
                        ->table('t_roleparams_char')
                        ->insert([
                            'rid' => $character->rid,
                            'idx' => 10,  // Monthly card index
                            'v3' => $monthlyCardValue  // Monthly card expiration value
                        ]);
                }

                \Log::info('Monthly card activated in game database', [
                    'user_id' => $user->ID,
                    'character_id' => $character->rid,
                    'character_name' => $character->rname,
                    'monthly_card_value' => $monthlyCardValue
                ]);
            } catch (\Exception $e) {
                // Log error but don't fail the purchase
                \Log::error('Failed to activate monthly card in game database: ' . $e->getMessage(), [
                    'user_id' => $user->ID,
                    'character_id' => $character->rid
                ]);
            }

            // Send bonus items to game if any (with error handling)
            if (!empty($package['bonus_items'])) {
                try {
                    $this->sendItemsToGameForCharacter($character, $package['bonus_items'], 'Thưởng mua thẻ tháng ' . $package['name']);
                } catch (\Exception $e) {
                    // Log error but don't fail the purchase
                    \Log::error('Failed to send bonus items to game: ' . $e->getMessage(), [
                        'user_id' => $user->ID,
                        'character_id' => $character->rid,
                        'package' => $package['name']
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Mua thẻ tháng thành công! Thẻ đã được kích hoạt cho nhân vật ' . $character->rname . ' và sẽ có hiệu lực trong ' . $package['duration_days'] . ' ngày.',
                'data' => [
                    'package_name' => $package['name'],
                    'character_name' => $character->rname,
                    'expires_at' => $expiresAt->format('d/m/Y H:i:s'),
                    'daily_reward' => $package['daily_reward_coins']
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi mua thẻ tháng: ' . $e->getMessage()
            ]);
        }
    }

    public function claimDaily(Request $request)
    {
        $userSession = Session::get('user_account');
        if (!$userSession) {
            return response()->json(['success' => false, 'message' => 'Vui lòng đăng nhập']);
        }

        $user = Account::find($userSession['id']);
        $cardId = $request->card_id;

        // Get the monthly card
        $card = DB::table('monthly_card_purchases')
            ->where('id', $cardId)
            ->where('user_id', $user->ID)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->first();

        if (!$card) {
            return response()->json(['success' => false, 'message' => 'Thẻ tháng không hợp lệ hoặc đã hết hạn']);
        }

        // Check if already claimed today
        $today = now()->format('Y-m-d');
        $lastClaimed = $card->last_claimed_at ? Carbon::parse($card->last_claimed_at)->format('Y-m-d') : null;

        if ($lastClaimed === $today) {
            return response()->json(['success' => false, 'message' => 'Bạn đã nhận thưởng hôm nay rồi']);
        }

        DB::beginTransaction();
        try {
            // Add daily reward coins
            DB::table('user_coins')
                ->where('account_id', $user->ID)
                ->update([
                    'coins' => DB::raw('coins + ' . $card->daily_reward_coins),
                    'updated_at' => now()
                ]);

            // Update card claim status
            DB::table('monthly_card_purchases')
                ->where('id', $cardId)
                ->update([
                    'last_claimed_at' => now(),
                    'days_claimed' => DB::raw('days_claimed + 1'),
                    'updated_at' => now()
                ]);

            // Send daily items to game if any (with error handling)
            $dailyItems = json_decode($card->daily_items, true);
            if (!empty($dailyItems)) {
                try {
                    $this->sendItemsToGame($user, $dailyItems, 'Thưởng hàng ngày thẻ tháng');
                } catch (\Exception $e) {
                    // Log error but don't fail the daily claim
                    \Log::error('Failed to send daily items to game: ' . $e->getMessage(), [
                        'user_id' => $user->ID,
                        'card_id' => $cardId
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Nhận thưởng hàng ngày thành công! +' . number_format($card->daily_reward_coins) . ' coin',
                'coins_received' => $card->daily_reward_coins
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ]);
        }
    }

    public function history(Request $request)
    {
        $user = Session::get('user_account');
        
        $query = MonthlyCardPurchase::where('user_id', $user['id']);

        // Filter by package type
        if ($request->has('package_type') && !empty($request->package_type)) {
            $query->where('package_type', $request->package_type);
        }

        // Filter by status
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        $cards = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('user.monthly-cards.history', compact('cards'));
    }

    public function show($id)
    {
        $user = Session::get('user_account');
        
        $monthlyCard = MonthlyCardPurchase::where('user_id', $user['id'])
            ->where('id', $id)
            ->firstOrFail();

        return view('user.monthly-cards.show', compact('monthlyCard'));
    }

    /**
     * Cancel user's own monthly card
     */
    public function cancelOwn(Request $request, $id)
    {
        $userSession = Session::get('user_account');
        if (!$userSession) {
            return response()->json(['success' => false, 'message' => 'Vui lòng đăng nhập']);
        }

        $user = Account::find($userSession['id']);

        // Get the monthly card (only user's own card)
        $card = DB::table('monthly_card_purchases')
            ->where('id', $id)
            ->where('user_id', $user->ID)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->first();

        if (!$card) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy thẻ tháng hoặc thẻ đã hết hạn'
            ]);
        }

        DB::beginTransaction();
        try {
            // Update card status to cancelled
            DB::table('monthly_card_purchases')
                ->where('id', $id)
                ->update([
                    'status' => 'cancelled',
                    'notes' => ($card->notes ?? '') . "\nHủy bởi người dùng: " . now()->format('d/m/Y H:i:s'),
                    'updated_at' => now()
                ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Đã hủy thẻ tháng thành công'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Add coins to user account (similar to card_month logic from old code)
     */
    private function addCoinsToUser($user, $amount)
    {
        // Get or create user coins record
        $userCoins = DB::table('user_coins')->where('account_id', $user->ID)->first();

        if ($userCoins) {
            DB::table('user_coins')
                ->where('account_id', $user->ID)
                ->update([
                    'coins' => DB::raw('coins + ' . $amount),
                    'updated_at' => now()
                ]);
        } else {
            DB::table('user_coins')->insert([
                'account_id' => $user->ID,
                'username' => $user->UserName,
                'coins' => $amount,
                'total_recharged' => 0,
                'total_spent' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        // Also add to game money (similar to old card_month logic)
        $this->addGameMoney($user, $amount);
    }

    /**
     * Add money to game database (updated for actual t_tempmoney structure)
     */
    private function addGameMoney($user, $amount)
    {
        $gameUserId = 'ZT' . str_pad($user->ID, 4, '0', STR_PAD_LEFT);

        // Create temp money record for game to process
        DB::connection('game_mysql')->table('t_tempmoney')->insert([
            'uid' => $gameUserId,
            'addmoney' => $amount
        ]);
    }

    /**
     * Send items to game mail system (similar to giftcode logic from old code)
     */
    private function sendItemsToGame($user, $items, $subject)
    {
        if (empty($items)) return;

        try {
            // Test game database connection first
            DB::connection('game_mysql')->getPdo();
        } catch (\Exception $e) {
            \Log::error('Game database connection failed: ' . $e->getMessage());
            throw new \Exception('Game database not available');
        }

        // Get user's game character
        $gameUserId = 'ZT' . str_pad($user->ID, 4, '0', STR_PAD_LEFT);

        try {
            $role = DB::connection('game_mysql')
                ->table('t_roles')
                ->where('userid', $gameUserId)
                ->where('isdel', 0)
                ->orderBy('lasttime', 'desc')
                ->first();
        } catch (\Exception $e) {
            \Log::error('Failed to get user character: ' . $e->getMessage());
            throw new \Exception('Cannot find user character in game');
        }

        if (!$role) {
            \Log::warning('No active character found for user', ['user_id' => $user->ID, 'game_user_id' => $gameUserId]);
            throw new \Exception('User has no active character in game');
        }

        // Create mail
        try {
            $mailid = DB::connection('game_mysql')->table('t_mail')->insertGetId([
                'senderrid' => 0,
                'senderrname' => 'System',
                'sendtime' => now(),
                'receiverrid' => $role->rid,
                'reveiverrname' => $role->rname,
                'subject' => $subject,
                'content' => 'Phần thưởng từ hệ thống thẻ tháng'
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to create mail in game: ' . $e->getMessage());
            throw new \Exception('Cannot create mail in game');
        }

        // Add items to mail (following old giftcode format)
        $mailItems = [];
        foreach ($items as $item) {
            if (is_string($item)) {
                // Parse item string format: "goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo"
                $parts = explode(',', $item);
                if (count($parts) >= 7) {
                    $mailItems[] = [
                        'mailid' => $mailid,
                        'goodsid' => $parts[0],
                        'gcount' => $parts[1],
                        'binding' => $parts[2],
                        'forge_level' => $parts[3],
                        'appendproplev' => $parts[4],
                        'lucky' => $parts[5],
                        'excellenceinfo' => $parts[6],
                    ];
                }
            } elseif (is_array($item)) {
                $mailItems[] = [
                    'mailid' => $mailid,
                    'goodsid' => $item['goodsid'] ?? $item[0] ?? 0,
                    'gcount' => $item['count'] ?? $item[1] ?? 1,
                    'binding' => $item['binding'] ?? $item[2] ?? 1,
                    'forge_level' => $item['forge_level'] ?? $item[3] ?? 0,
                    'appendproplev' => $item['appendproplev'] ?? $item[4] ?? 0,
                    'lucky' => $item['lucky'] ?? $item[5] ?? 0,
                    'excellenceinfo' => $item['excellenceinfo'] ?? $item[6] ?? 0,
                ];
            }
        }

        if (!empty($mailItems)) {
            try {
                DB::connection('game_mysql')->table('t_mailgoods')->insert($mailItems);
                \Log::info('Successfully sent items to game', [
                    'user_id' => $user->ID,
                    'character' => $role->rname,
                    'items_count' => count($mailItems),
                    'mail_id' => $mailid
                ]);
            } catch (\Exception $e) {
                \Log::error('Failed to insert mail items: ' . $e->getMessage());
                throw new \Exception('Cannot add items to mail');
            }
        }
    }

    /**
     * Send items to specific character in game
     */
    private function sendItemsToGameForCharacter($character, $items, $subject)
    {
        if (empty($items)) return;

        try {
            // Test game database connection first
            DB::connection('game_mysql')->getPdo();
        } catch (\Exception $e) {
            \Log::error('Game database connection failed: ' . $e->getMessage());
            throw new \Exception('Game database not available');
        }

        // Create mail
        try {
            $mailid = DB::connection('game_mysql')->table('t_mail')->insertGetId([
                'senderrid' => 0,
                'senderrname' => 'System',
                'sendtime' => now(),
                'receiverrid' => $character->rid,
                'reveiverrname' => $character->rname,
                'subject' => $subject,
                'content' => 'Phần thưởng từ hệ thống thẻ tháng'
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to create mail in game: ' . $e->getMessage());
            throw new \Exception('Cannot create mail in game');
        }

        // Add items to mail (following old giftcode format)
        $mailItems = [];
        foreach ($items as $item) {
            if (is_string($item)) {
                // Parse item string format: "goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo"
                $parts = explode(',', $item);
                if (count($parts) >= 7) {
                    $mailItems[] = [
                        'mailid' => $mailid,
                        'goodsid' => $parts[0],
                        'gcount' => $parts[1],
                        'binding' => $parts[2],
                        'forge_level' => $parts[3],
                        'appendproplev' => $parts[4],
                        'lucky' => $parts[5],
                        'excellenceinfo' => $parts[6],
                    ];
                }
            } elseif (is_array($item)) {
                $mailItems[] = [
                    'mailid' => $mailid,
                    'goodsid' => $item['goodsid'] ?? $item[0] ?? 0,
                    'gcount' => $item['count'] ?? $item[1] ?? 1,
                    'binding' => $item['binding'] ?? $item[2] ?? 1,
                    'forge_level' => $item['forge_level'] ?? $item[3] ?? 0,
                    'appendproplev' => $item['appendproplev'] ?? $item[4] ?? 0,
                    'lucky' => $item['lucky'] ?? $item[5] ?? 0,
                    'excellenceinfo' => $item['excellenceinfo'] ?? $item[6] ?? 0,
                ];
            }
        }

        if (!empty($mailItems)) {
            try {
                DB::connection('game_mysql')->table('t_mailgoods')->insert($mailItems);
                \Log::info('Successfully sent items to specific character', [
                    'character_id' => $character->rid,
                    'character_name' => $character->rname,
                    'items_count' => count($mailItems),
                    'mail_id' => $mailid
                ]);
            } catch (\Exception $e) {
                \Log::error('Failed to insert mail items: ' . $e->getMessage());
                throw new \Exception('Cannot add items to mail');
            }
        }
    }



    /**
     * Get monthly card packages configuration - Updated to single package with 1,000,000 coins
     */
    private function getMonthlyCardPackages()
    {
        return [
            'premium' => [
                'name' => 'Thẻ Tháng Premium',
                'cost_coins' => 1500000, // Updated to 1,500,000 coins as requested
                'rmb_amount' => 45, // RMB amount for game server
                'duration_days' => 30,
                'daily_reward_coins' => 15000, // Increased daily reward proportionally
                'description' => 'Thẻ tháng cao cấp - Nhận 15,000 coin + items đặc biệt mỗi ngày trong 30 ngày',
                'bonus_items' => [
                    '14,20,1,0,0,0,0', // Jewel of Bless x20
                    '15,50,1,0,0,0,0', // Jewel of Soul x50
                    '16,5,1,0,0,0,0',  // Jewel of Life x5
                    '12,2,1,0,0,0,0'   // Box of Luck x2
                ],
                'daily_items' => [
                    '14,5,1,0,0,0,0', // Jewel of Bless x5 daily
                    '15,3,1,0,0,0,0'  // Jewel of Soul x3 daily
                ]
            ]
        ];
    }
}
