<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class Giftcode extends Model
{
    use HasFactory;

    protected $connection = 'mysql'; // Website database
    protected $table = 't_giftcode';

    protected $fillable = [
        'type',
        'accounts',
        'multiple',
        'code',
        'items',
        'content',
        'limit',
        'period',
        'zoneid'
    ];

    protected $casts = [
        'multiple' => 'boolean',
        'type' => 'integer',
        'limit' => 'integer',
        'period' => 'integer',
        'zoneid' => 'integer'
    ];

    // Helper methods for giftcode validation and usage
    public function getCodesArray()
    {
        if (is_string($this->code)) {
            $decoded = json_decode($this->code, true);
            return is_array($decoded) ? $decoded : [$this->code];
        }
        return is_array($this->code) ? $this->code : [$this->code];
    }

    public function getItemsArray()
    {
        if (is_string($this->items)) {
            $decoded = json_decode($this->items, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($this->items) ? $this->items : [];
    }

    // Mutators to handle JSON encoding
    public function setCodeAttribute($value)
    {
        $this->attributes['code'] = is_array($value) ? json_encode($value) : $value;
    }

    public function setItemsAttribute($value)
    {
        $this->attributes['items'] = is_array($value) ? json_encode($value) : $value;
    }

    public function getAllowedAccounts()
    {
        if (empty($this->accounts)) {
            return [];
        }
        return array_map('trim', explode(',', $this->accounts));
    }

    // Constants for gift code types
    public const TYPE_PUBLIC = 1;      // Công khai - ai cũng dùng được
    public const TYPE_PRIVATE = 2;     // Riêng tư - chỉ tài khoản cụ thể
    public const TYPE_CHARACTER = 0;   // Theo nhân vật - mỗi nhân vật dùng 1 lần

    public function logs()
    {
        return $this->hasMany(GiftcodeLog::class, 'groupid');
    }

    public function isExpired()
    {
        if ($this->period <= 0) return false;

        $createdAt = Carbon::parse($this->created_at);
        $expiryDate = $createdAt->addDays($this->period);
        return Carbon::now() > $expiryDate;
    }

    public function isUsedUp()
    {
        if ($this->limit <= 0) return false;

        $usedCount = GiftcodeLog::where('groupid', $this->id)->count();
        return $usedCount >= $this->limit;
    }

    public function getUsageCount()
    {
        return GiftcodeLog::where('groupid', $this->id)->count();
    }

    public function canBeUsed()
    {
        return $this->is_active && !$this->isExpired() && !$this->isUsedUp();
    }

    public function getUsagePercentage()
    {
        if ($this->limit <= 0) return 0;
        $usedCount = GiftcodeLog::where('groupid', $this->id)->count();
        return ($usedCount / $this->limit) * 100;
    }

    public function getUsedCount()
    {
        return GiftcodeLog::where('groupid', $this->id)->count();
    }

    public function getRemainingUses()
    {
        if ($this->limit <= 0) return 'Không giới hạn';
        return max(0, $this->limit - $this->getUsedCount());
    }

    // Check if user can use this giftcode
    public function canBeUsedByUser($userId, $characterId = null)
    {
        if (!$this->canBeUsed()) {
            return false;
        }

        // Check if user is allowed (for private type)
        if ($this->type == self::TYPE_PRIVATE) {
            $allowedAccounts = $this->getAllowedAccounts();
            if (empty($allowedAccounts)) {
                return false;
            }

            // Get user info from account database
            $user = DB::connection('mysql')->table('t_account')->where('ID', $userId)->first();
            if (!$user || !in_array($user->UserName, $allowedAccounts)) {
                return false;
            }
        }

        // Check if already used
        $query = GiftcodeLog::where('groupid', $this->id)->where('uid', $userId);

        if ($this->type == self::TYPE_CHARACTER && $characterId) {
            $query->where('rid', $characterId);
        }

        return !$query->exists();
    }

    // Get formatted items list for display
    public function getFormattedItems()
    {
        if (!$this->items || !is_array($this->items)) {
            return [];
        }

        $formatted = [];
        foreach ($this->items as $item) {
            $parts = explode(',', $item);
            if (count($parts) >= 2) {
                $formatted[] = [
                    'id' => $parts[0],
                    'count' => $parts[1],
                    'binding' => $parts[2] ?? 0,
                    'forge_level' => $parts[3] ?? 0,
                    'appendproplev' => $parts[4] ?? 0,
                    'lucky' => $parts[5] ?? 0,
                    'excellenceinfo' => $parts[6] ?? 0,
                ];
            }
        }
        return $formatted;
    }
}
